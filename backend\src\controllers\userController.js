const User = require("../models/User");
const jwt = require("jsonwebtoken");

exports.getAllUsers = async (req, res) => {
  try {
    const users = await User.findAll();
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.createUser = async (req, res) => {
  const { username, password, roleIds } = req.body;
  try {
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: "Username already exists" });
    }
    const userId = await User.create(username, password, roleIds);
    res.status(201).json({ message: "User created successfully", userId });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.updateUser = async (req, res) => {
  const { id } = req.params;
  const { username, password, roleIds } = req.body;
  const loggedInUser = req.user;

  // Otorisasi: Hanya admin atau pengguna itu sendiri yang dapat memperbarui
  if (
    loggedInUser.id !== parseInt(id) &&
    !loggedInUser.roles.includes("admin")
  ) {
    return res.status(403).json({ message: "User not authorized" });
  }

  // Hanya admin yang dapat mengubah peran
  const rolesToUpdate = loggedInUser.roles.includes("admin")
    ? roleIds
    : undefined;

  try {
    const userBeforeUpdate = await User.findById(id);
    const oldUsername = userBeforeUpdate[0].username;

    await User.update(id, username, password, rolesToUpdate);

    // Jika username diubah, keluarkan token baru
    if (oldUsername !== username) {
      const userRoles = await User.findById(id);
      const roles = userRoles.map((r) => r.role);
      const payload = {
        user: { id: parseInt(id), username, roles },
      };
      const token = jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: "1h",
      });
      return res.json({ message: "User updated successfully", token });
    }

    res.json({ message: "User updated successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.deleteUser = async (req, res) => {
  const { id } = req.params;
  try {
    await User.delete(id);
    res.json({ message: "User deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};
