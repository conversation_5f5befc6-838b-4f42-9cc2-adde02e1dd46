const db = require("../config/db");
const bcrypt = require("bcryptjs");

const saltRounds = 10;

const User = {
  create: async (username, password, roleIds) => {
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    const [result] = await db.query(
      "INSERT INTO users (username, password) VALUES (?, ?)",
      [username, hashedPassword]
    );
    const userId = result.insertId;

    let assignedRoleIds = roleIds && roleIds.length ? roleIds : [];
    if (!assignedRoleIds.length) {
      // default role = 'member'
      const [rows] = await db.query(
        "SELECT id FROM roles WHERE name = 'member' LIMIT 1"
      );
      if (rows.length) assignedRoleIds = [rows[0].id];
    }

    if (assignedRoleIds.length) {
      const userRoles = assignedRoleIds.map((roleId) => [userId, roleId]);
      await db.query("INSERT INTO user_roles (user_id, role_id) VALUES ?", [
        userRoles,
      ]);
    }
    return userId;
  },

  findByUsername: async (username) => {
    const [rows] = await db.query("SELECT * FROM users WHERE username = ?", [
      username,
    ]);
    return rows[0];
  },

  findById: async (id) => {
    const [rows] = await db.query(
      `
      SELECT u.id, u.username, r.name as role 
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.id = ?
    `,
      [id]
    );
    return rows;
  },

  comparePassword: async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
  },

  findAll: async () => {
    const [rows] = await db.query(`
      SELECT u.id, u.username, GROUP_CONCAT(r.name) as roles
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      GROUP BY u.id
    `);
    return rows;
  },

  update: async (id, username, password, roleIds) => {
    if (password) {
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      await db.query(
        "UPDATE users SET username = ?, password = ? WHERE id = ?",
        [username, hashedPassword, id]
      );
    } else {
      await db.query("UPDATE users SET username = ? WHERE id = ?", [
        username,
        id,
      ]);
    }

    await db.query("DELETE FROM user_roles WHERE user_id = ?", [id]);
    if (roleIds && roleIds.length) {
      const userRoles = roleIds.map((roleId) => [id, roleId]);
      await db.query("INSERT INTO user_roles (user_id, role_id) VALUES ?", [
        userRoles,
      ]);
    }
  },

  delete: async (id) => {
    await db.query("DELETE FROM users WHERE id = ?", [id]);
  },
};

module.exports = User;
