import { MapPin, Phone, Mail, MessageCircle, Send } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const KontakSection = () => {
  return (
    <section id="kontak" className="py-20 bg-gradient-section">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
              <MapPin className="h-6 w-6 text-white" />
            </div>
            <h2 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
              Lokasi & Kontak
            </h2>
          </div>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Kunjungi kami atau hubungi tim An Nabawi untuk informasi lebih
            lanjut
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Left Content - Contact Info & Form */}
            <div className="space-y-8">
              {/* Contact Information */}
              <div className="space-y-6">
                <h3 className="font-poppins font-bold text-2xl text-islamic-green mb-6">
                  Informasi Kontak
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-islamic-green rounded-full flex items-center justify-center flex-shrink-0">
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-islamic-green mb-1">
                        Alamat
                      </h4>
                      <p className="text-muted-foreground">
                        Jl. Wijaya Kusuma, RT.001/RW.003
                        <br />
                        Poris Plawad Indah, Kec. Cipondoh,
                        <br />
                        Kota Tangerang, Banten 15141
                        <br />
                        Indonesia
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-islamic-green rounded-full flex items-center justify-center flex-shrink-0">
                      <Phone className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-islamic-green mb-1">
                        Telepon
                      </h4>
                      <p className="text-muted-foreground">
                        +62 21 1234 5678
                        <br />
                        +62 812 3456 7890
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-islamic-green rounded-full flex items-center justify-center flex-shrink-0">
                      <Mail className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-islamic-green mb-1">
                        Email
                      </h4>
                      <p className="text-muted-foreground">
                        <EMAIL>
                        <br />
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-islamic-green rounded-full flex items-center justify-center flex-shrink-0">
                      <MessageCircle className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-islamic-green mb-1">
                        WhatsApp
                      </h4>
                      <p className="text-muted-foreground mb-2">
                        Bergabung dengan channel WhatsApp resmi kami
                      </p>
                      <a
                        href="https://www.whatsapp.com/channel/0029VaTTWVAGufIryonAMo1z"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button variant="islamic" size="sm">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Join WhatsApp Channel
                        </Button>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-poppins text-xl text-islamic-green">
                    Kirim Pesan
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-islamic-green mb-2">
                        Nama Lengkap
                      </label>
                      <Input placeholder="Masukkan nama Anda" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-islamic-green mb-2">
                        Email
                      </label>
                      <Input type="email" placeholder="<EMAIL>" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-islamic-green mb-2">
                      Subjek
                    </label>
                    <Input placeholder="Subjek pesan" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-islamic-green mb-2">
                      Pesan
                    </label>
                    <Textarea
                      placeholder="Tulis pesan Anda di sini..."
                      rows={4}
                    />
                  </div>
                  <Button variant="islamic" className="w-full">
                    <Send className="mr-2 h-4 w-4" />
                    Kirim Pesan
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Right Content - Map */}
            <div className="space-y-8">
              <div>
                <h3 className="font-poppins font-bold text-2xl text-islamic-green mb-6">
                  Lokasi Masjid
                </h3>

                {/* Google Maps Embed */}
                <div className="rounded-xl overflow-hidden shadow-lg">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d569.1786360346682!2d106.65827329532193!3d-6.192840469807917!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f9174ea7502b%3A0x4e4c1293ea03c069!2sMasjid%20An%20Nabawi!5e0!3m2!1sen!2sid!4v1754454833627!5m2!1sen!2sid"
                    width="100%"
                    height="400"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  ></iframe>
                </div>

                <div className="mt-4 text-center">
                  <a
                    href="https://maps.app.goo.gl/Ey8jhiWQAGfux6iY7"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button variant="islamic-outline">
                      <MapPin className="mr-2 h-4 w-4" />
                      Buka di Google Maps
                    </Button>
                  </a>
                </div>
              </div>

              {/* Opening Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-poppins text-xl text-islamic-green">
                    Jam Operasional
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium">Senin - Kamis</span>
                    <span className="text-muted-foreground">05:00 - 22:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Jum'at</span>
                    <span className="text-muted-foreground">05:00 - 23:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Sabtu - Minggu</span>
                    <span className="text-muted-foreground">05:00 - 22:00</span>
                  </div>
                  <div className="border-t pt-3 mt-4">
                    <div className="flex justify-between">
                      <span className="font-medium text-islamic-green">
                        Sholat Jum'at
                      </span>
                      <span className="text-islamic-green font-medium">
                        12:30 - 13:00
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default KontakSection;
