import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/components/ui/sonner";
import { useNavigate, useParams } from "react-router-dom";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import CreatableSelect from "react-select/creatable";
import { ArrowLeft } from "lucide-react";

interface TagOption {
  readonly label: string;
  readonly value: string;
}

const EditArticle = () => {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState<readonly TagOption[]>([]);
  const [tagOptions, setTagOptions] = useState<readonly TagOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { token } = useAuth();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  useEffect(() => {
    const fetchAllTags = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/tags", {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!response.ok) throw new Error("Failed to fetch tags");
        const data = await response.json();
        setTagOptions(data.map((tag: string) => ({ value: tag, label: tag })));
      } catch (error) {
        // silent fail
      }
    };

    const fetchArticle = async () => {
      try {
        const response = await fetch(
          `http://localhost:3001/api/articles/${id}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!response.ok) throw new Error("Failed to fetch article");
        const data = await response.json();
        setTitle(data.title);
        setSlug(data.slug);
        setContent(data.content);
        const tagsResponse = await fetch(
          `http://localhost:3001/api/articles/${id}/tags`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!tagsResponse.ok) throw new Error("Failed to fetch tags");
        const tagsData = await tagsResponse.json();
        setTags(tagsData.map((tag: string) => ({ value: tag, label: tag })));
      } catch (error) {
        toast.error(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
        navigate("/dashboard/articles");
      } finally {
        setIsLoading(false);
      }
    };
    if (token) {
      fetchAllTags();
      fetchArticle();
    }
  }, [id, token, navigate]);

  const handleSubmit = async (status: "draft" | "published") => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`http://localhost:3001/api/articles/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          slug,
          content,
          status,
          tags: tags.map((t) => t.value),
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to update article");
      }
      toast.success(`Article successfully updated!`);
      navigate("/dashboard/articles");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <p>Loading article...</p>;
  }

  return (
    <div className="grid gap-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={() => navigate("/dashboard/articles")}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="flex-1 shrink-0 whitespace-nowrap text-xl font-semibold tracking-tight sm:grow-0">
          Edit Article
        </h1>
      </div>
      <Card>
        <CardContent className="pt-6">
          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <CreatableSelect
                isMulti
                options={tagOptions}
                value={tags}
                onChange={(newValue) => setTags(newValue)}
                onCreateOption={(inputValue) => {
                  const newTag = { value: inputValue, label: inputValue };
                  setTagOptions((prev) => [...prev, newTag]);
                  setTags((prev) => [...prev, newTag]);
                }}
                placeholder="Select or create tags"
                styles={{
                  menu: (provided) => ({ ...provided, zIndex: 9999 }),
                }}
              />
            </div>
            <div className="space-y-2">
              <Label>Content</Label>
              <CKEditor
                editor={ClassicEditor as any}
                data={content}
                onChange={(event, editor) => {
                  const data = editor.getData();
                  setContent(data);
                }}
                config={{
                  ckfinder: {
                    uploadUrl: `http://localhost:3001/api/upload?token=${token}`,
                  },
                }}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => handleSubmit("draft")}
                disabled={isSubmitting}
              >
                Save as Draft
              </Button>
              <Button
                onClick={() => handleSubmit("published")}
                disabled={isSubmitting}
              >
                Publish Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditArticle;
