import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface City {
  id: string;
  lokasi: string;
}

interface CitySearchProps {
  onCitySelect: (cityId: string) => void;
}

export function CitySearch({ onCitySelect }: CitySearchProps) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState("");
  const [cities, setCities] = React.useState<City[]>([]);

  React.useEffect(() => {
    const fetchCities = async () => {
      try {
        const response = await fetch(
          "https://api.myquran.com/v2/sholat/kota/semua"
        );
        const data = await response.json();
        if (data.status) {
          setCities(data.data);
        }
      } catch (error) {
        console.error("Failed to fetch cities:", error);
      }
    };
    fetchCities();
  }, []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          {value
            ? cities.find((city) => city.lokasi.toLowerCase() === value)?.lokasi
            : "Pilih Kota..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Cari kota..." />
          <CommandList>
            <CommandEmpty>Kota tidak ditemukan.</CommandEmpty>
            <CommandGroup>
              {cities.map((city) => (
                <CommandItem
                  key={city.id}
                  value={city.lokasi}
                  onSelect={(currentValue) => {
                    // currentValue is the 'value' prop of the CommandItem, which is city.lokasi
                    // We need to find the city object to get its ID.
                    const selectedCity = cities.find(
                      (c) =>
                        c.lokasi.toLowerCase() === currentValue.toLowerCase()
                    );
                    if (selectedCity) {
                      setValue(selectedCity.lokasi); // Set the display value
                      onCitySelect(selectedCity.id); // Pass the ID to the parent
                    }
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === city.lokasi.toLowerCase()
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {city.lokasi}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
