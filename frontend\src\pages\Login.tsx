import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import Logo from "@/assets/logo.png";

const Login = () => {
  const { login } = useAuth();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("http://localhost:3001/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Login gagal");
      }

      const data = await response.json();
      login(data.token);
      navigate("/dashboard");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Login gagal. Periksa kembali username dan password Anda.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="flex min-h-screen w-full items-center justify-center bg-cover bg-center p-4"
      style={{ backgroundImage: `url('/src/assets/hero-bg.jpg')` }}
    >
      <div className="absolute inset-0 bg-islamic-green/80 backdrop-blur-sm"></div>
      <Card className="relative z-10 w-full max-w-md border-islamic-gold/50 bg-white/90 shadow-2xl">
        <CardHeader className="text-center">
          <img src={Logo} alt="An-Nabawi Logo" className="mx-auto mb-4 h-20" />
          <CardTitle className="font-amiri text-3xl text-islamic-green">
            Admin Panel
          </CardTitle>
          <CardDescription className="text-gray-600">
            Masuk untuk mengelola konten An-Nabawi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="username" className="text-islamic-green">
                  Username
                </Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Masukkan username Anda"
                  required
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password" className="text-islamic-green">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  required
                  placeholder="Masukkan password Anda"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              {error && (
                <p className="text-red-500 text-sm text-center">{error}</p>
              )}
              <Button
                type="submit"
                variant="islamic"
                className="w-full"
                disabled={loading}
              >
                {loading ? "Loading..." : "Login"}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="mt-4 text-center text-sm text-gray-600">
            Belum punya akun?{" "}
            <Link
              to="/register"
              className="font-semibold text-islamic-green hover:text-islamic-gold hover:underline"
            >
              Register
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
