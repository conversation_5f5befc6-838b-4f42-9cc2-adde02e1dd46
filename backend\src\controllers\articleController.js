const Article = require("../models/Article");
const Tag = require("../models/Tag");

// @desc    Create an article
exports.createArticle = async (req, res) => {
  const { title, slug, content, featured_image_url, status, tags } = req.body; // `tags` is an array of strings
  try {
    const existingSlug = await Article.findBySlug(slug);
    if (existingSlug) {
      return res.status(400).json({ message: "Slug already exists" });
    }

    const articleId = await Article.create(
      title,
      slug,
      content,
      featured_image_url,
      req.user.id,
      status
    );

    if (tags && tags.length > 0) {
      const tagIds = await Promise.all(
        tags.map((tagName) => Tag.findOrCreate(tagName))
      );
      await Tag.linkToArticle(articleId, tagIds);
    }

    res
      .status(201)
      .json({ message: "Article created successfully", articleId });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get all articles
exports.getAllArticles = async (req, res) => {
  try {
    const articles = await Article.findAll();
    res.json(articles);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get single article by ID
exports.getArticleById = async (req, res) => {
  try {
    const article = await Article.findById(req.params.id);
    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }
    res.json(article);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Update an article
exports.updateArticle = async (req, res) => {
  const { title, slug, content, featured_image_url, status, tags } = req.body;
  try {
    let article = await Article.findById(req.params.id);
    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }

    // Check user
    if (
      article.author_id.toString() !== req.user.id.toString() &&
      !req.user.roles.includes("admin")
    ) {
      return res.status(401).json({ message: "User not authorized" });
    }

    const existingSlug = await Article.findBySlug(slug);
    if (existingSlug && existingSlug.id.toString() !== req.params.id) {
      return res.status(400).json({ message: "Slug already exists" });
    }

    const affectedRows = await Article.update(
      req.params.id,
      title,
      slug,
      content,
      featured_image_url,
      status
    );

    if (tags) {
      const tagIds = await Promise.all(
        tags.map((tagName) => Tag.findOrCreate(tagName))
      );
      await Tag.unlinkFromArticle(req.params.id); // Hapus tag lama
      await Tag.linkToArticle(req.params.id, tagIds); // Tautkan tag baru
    }

    if (affectedRows > 0) {
      res.json({ message: "Article updated" });
    } else {
      res.status(400).json({ message: "Article could not be updated" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Delete an article
exports.deleteArticle = async (req, res) => {
  try {
    let article = await Article.findById(req.params.id);
    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }

    // Check user
    if (
      article.author_id.toString() !== req.user.id.toString() &&
      !req.user.roles.includes("admin")
    ) {
      return res.status(401).json({ message: "User not authorized" });
    }

    const affectedRows = await Article.delete(req.params.id);
    if (affectedRows > 0) {
      res.json({ message: "Article removed" });
    } else {
      res.status(404).json({ message: "Article not found" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get single article by slug
exports.getArticleBySlug = async (req, res) => {
  try {
    const article = await Article.findBySlug(req.params.slug);
    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }
    res.json(article);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Check if a slug exists
exports.checkSlug = async (req, res) => {
  const { slug, currentId } = req.body;
  try {
    const article = await Article.findBySlug(slug);
    if (article && article.id.toString() !== currentId) {
      return res.json({ exists: true });
    }
    res.json({ exists: false });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};
