import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/components/ui/sonner";
import { useNavigate } from "react-router-dom";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import CreatableSelect from "react-select/creatable";
import { debounce } from "lodash";
import { ArrowLeft } from "lucide-react";

interface TagOption {
  readonly label: string;
  readonly value: string;
}

const NewArticle = () => {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState<readonly TagOption[]>([]);
  const [tagOptions, setTagOptions] = useState<readonly TagOption[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slugError, setSlugError] = useState<string | null>(null);
  const { token } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchTags = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/tags", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        const data = await response.json();
        const options = data.map((tag: string) => ({
          value: tag,
          label: tag,
        }));
        setTagOptions(options);
      } catch (error) {
        // Silently ignore
      }
    };
    if (token) {
      fetchTags();
    }
  }, [token]);

  const generateSlug = (str: string) => {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-");
  };

  const checkSlugUniqueness = useCallback(
    debounce(async (currentSlug: string) => {
      if (!currentSlug) {
        setSlugError(null);
        return;
      }
      try {
        const response = await fetch(
          "http://localhost:3001/api/articles/check-slug",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ slug: currentSlug }),
          }
        );
        const data = await response.json();
        if (data.exists) {
          setSlugError("Slug already exists. Please choose another one.");
        } else {
          setSlugError(null);
        }
      } catch (error) {
        // Handle error silently
      }
    }, 500),
    [token]
  );

  useEffect(() => {
    const newSlug = generateSlug(title);
    setSlug(newSlug);
    checkSlugUniqueness(newSlug);
  }, [title, checkSlugUniqueness]);

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value;
    setSlug(newSlug);
    checkSlugUniqueness(newSlug);
  };

  const handleSubmit = async (status: "draft" | "published") => {
    if (slugError) {
      toast.error("Please fix the errors before submitting.");
      return;
    }
    setIsSubmitting(true);
    try {
      const response = await fetch("http://localhost:3001/api/articles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          slug,
          content,
          status,
          tags: tags.map((t) => t.value),
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create article");
      }
      toast.success(`Article successfully ${status}!`);
      navigate("/dashboard/articles");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="grid gap-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={() => navigate("/dashboard/articles")}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="flex-1 shrink-0 whitespace-nowrap text-xl font-semibold tracking-tight sm:grow-0">
          Add New Article
        </h1>
      </div>
      <Card>
        <CardContent className="pt-6">
          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter article title"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={slug}
                onChange={handleSlugChange}
                placeholder="article-slug-will-be-here"
                required
              />
              {slugError && (
                <p className="text-sm text-red-500 mt-1">{slugError}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <CreatableSelect
                isMulti
                options={tagOptions}
                value={tags}
                onChange={(newValue) => setTags(newValue)}
                onCreateOption={(inputValue) => {
                  const newTag = { value: inputValue, label: inputValue };
                  setTagOptions((prev) => [...prev, newTag]);
                  setTags((prev) => [...prev, newTag]);
                }}
                placeholder="Select or create tags"
                styles={{
                  menu: (provided) => ({ ...provided, zIndex: 9999 }),
                }}
              />
            </div>
            <div className="space-y-2">
              <Label>Content</Label>
              <CKEditor
                editor={ClassicEditor as any}
                data={content}
                onChange={(event, editor) => {
                  const data = editor.getData();
                  setContent(data);
                }}
                config={{
                  ckfinder: {
                    uploadUrl: `http://localhost:3001/api/upload?token=${token}`,
                  },
                }}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => handleSubmit("draft")}
                disabled={isSubmitting || !!slugError}
              >
                Save as Draft
              </Button>
              <Button
                onClick={() => handleSubmit("published")}
                disabled={isSubmitting || !!slugError}
              >
                Publish
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewArticle;
