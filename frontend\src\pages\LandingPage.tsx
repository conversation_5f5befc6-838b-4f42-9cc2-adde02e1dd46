import { useState, useEffect } from "react";
import Navigation from "@/components/Navigation";
import HeroSection from "@/components/HeroSection";
import JadwalSholatSection from "@/components/JadwalSholatSection";
import KajianSection from "@/components/KajianSection";
import ArtikelSection from "@/components/ArtikelSection";
import DownloadAppSection from "@/components/DownloadAppSection";
import KontakSection from "@/components/KontakSection";
import Footer from "@/components/Footer";
import usePrayerTimes from "@/hooks/usePrayerTimes";
import useHijriDate from "@/hooks/useHijriDate";
import useLocation from "@/hooks/useLocation";

interface Article {
  id: number;
  title: string;
  slug: string;
  content: string;
  featured_image_url: string;
  author_name: string;
  created_at: string;
  tags: string[];
}

interface Event {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
  description: string;
  tags: string[];
}

const LandingPage = () => {
  const [cityId, setCityId] = useState<string | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [articles, setArticles] = useState<Article[]>([]);
  const [articlesLoading, setArticlesLoading] = useState(true);
  const [events, setEvents] = useState<Event[]>([]);
  const [eventsLoading, setEventsLoading] = useState(true);

  const location = useLocation();
  const prayerTimes = usePrayerTimes(cityId);
  const hijriDate = useHijriDate();

  useEffect(() => {
    if (location.error) {
      setLocationError(location.error);
      return;
    }

    if (location.latitude && location.longitude) {
      const findCityId = async () => {
        try {
          const allCitiesResponse = await fetch(
            "https://api.myquran.com/v2/sholat/kota/semua"
          );
          const allCitiesData = await allCitiesResponse.json();
          if (!allCitiesData.status)
            throw new Error("Failed to fetch city list.");

          const geoResponse = await fetch(
            `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${location.latitude}&lon=${location.longitude}`
          );
          const geoData = await geoResponse.json();

          const displayName = geoData.display_name.toUpperCase();
          let foundCity = null;
          for (const apiCity of allCitiesData.data) {
            const normalizedApiLokasi = apiCity.lokasi
              .replace(/^(KAB\. |KOTA )/i, "")
              .toUpperCase();
            if (displayName.includes(normalizedApiLokasi)) {
              foundCity = apiCity;
              break;
            }
          }

          if (foundCity) {
            setCityId(foundCity.id);
          } else {
            setLocationError(
              "Could not automatically determine your city. Please select it manually."
            );
          }
        } catch (error) {
          setLocationError(
            "Failed to determine location. Please select your city manually."
          );
        }
      };
      findCityId();
    }

    const fetchArticles = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/articles");
        if (!response.ok) throw new Error("Failed to fetch articles");
        const data = await response.json();
        setArticles(data);
      } catch (error) {
        console.error(error);
      } finally {
        setArticlesLoading(false);
      }
    };

    const fetchEvents = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/events");
        if (!response.ok) throw new Error("Failed to fetch events");
        const data = await response.json();
        setEvents(data);
      } catch (error) {
        console.error(error);
      } finally {
        setEventsLoading(false);
      }
    };

    fetchArticles();
    fetchEvents();
  }, [location]);

  const handleCitySelect = (selectedCityId: string) => {
    setCityId(selectedCityId);
    setLocationError(null); // Clear location error when user selects manually
  };

  return (
    <div className="min-h-screen font-inter">
      <Navigation />
      <HeroSection
        hijriDate={hijriDate.date}
        loading={
          hijriDate.loading ||
          (location.latitude !== null && prayerTimes.loading)
        }
      />
      <JadwalSholatSection
        prayerTimes={prayerTimes}
        locationError={locationError}
        onCitySelect={handleCitySelect}
      />
      <KajianSection events={events} isLoading={eventsLoading} />
      <ArtikelSection articles={articles} isLoading={articlesLoading} />
      <DownloadAppSection />
      <KontakSection />
      <Footer />
    </div>
  );
};

export default LandingPage;
