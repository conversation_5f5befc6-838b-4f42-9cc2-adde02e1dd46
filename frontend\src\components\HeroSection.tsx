import { ArrowDown, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-bg.jpg";
import { Skeleton } from "@/components/ui/skeleton";
import Logo from "@/assets/logo.png";

interface HeroSectionProps {
  hijriDate: string | null;
  loading: boolean;
}

const HeroSection = ({ hijriDate, loading }: HeroSectionProps) => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="hero"
      className="min-h-screen flex items-center justify-center relative overflow-hidden pt-16"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 128, 55, 0.8), rgba(10, 92, 54, 0.9)), url(${heroImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundAttachment: "fixed",
      }}
    >
      {/* Decorative Islamic Pattern Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-islamic-green/20 via-transparent to-islamic-green-light/30" />

      {/* Content */}
      <div className="container mx-auto px-4 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Logo */}
          <div className="mb-4 sm:mb-6 flex justify-center">
            <img
              src={Logo}
              alt="An Nabawi Logo"
              className="w-28 h-28 sm:w-24 sm:h-24 md:w-28 md:h-28 drop-shadow-lg hover:scale-105 transition-transform duration-300"
            />
          </div>

          {/* Main Heading */}
          <h1 className="font-poppins font-bold text-3xl sm:text-3xl md:text-4xl lg:text-5xl text-white mb-3 sm:mb-4 leading-tight">
            Selamat Datang di
            <br />
            <span className="text-islamic-gold">An Nabawi</span>
          </h1>

          {/* Islamic Date */}
          {loading ? (
            <Skeleton className="h-8 w-1/2 mx-auto mb-4" />
          ) : (
            hijriDate && (
              <p className="font-poppins font-medium text-base md:text-lg lg:text-xl text-islamic-gold/90 mb-4 tracking-wide">
                {hijriDate}
              </p>
            )
          )}

          {/* Subheading */}
          <p className="font-inter text-lg sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6 max-w-2xl mx-auto leading-relaxed">
            Pusat Kajian & Dakwah Islam
          </p>

          <p className="font-inter text-base sm:text-base text-white/80 mb-6 sm:mb-8 max-w-3xl mx-auto px-2">
            Bergabunglah dengan komunitas Muslim yang aktif dalam menuntut ilmu
            dan memperkuat iman melalui kajian-kajian berkualitas dan program
            dakwah yang inspiratif.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-8 sm:mb-12 px-4">
            <Button
              variant="islamic"
              size="lg"
              onClick={() => scrollToSection("jadwal-sholat")}
              className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg min-w-[200px]"
            >
              <ArrowDown className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
              Lihat Jadwal Sholat
            </Button>
            <Button
              variant="islamic-outline"
              size="lg"
              onClick={() => scrollToSection("download-app")}
              className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg min-w-[200px] bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-islamic-green"
            >
              <Download className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
              Download Aplikasi
            </Button>
          </div>

          {/* Scroll Indicator */}
          <div className="animate-bounce hidden sm:block">
            <ArrowDown className="h-6 w-6 sm:h-8 sm:w-8 text-white/60 mx-auto" />
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" className="w-full h-20 fill-background">
          <path d="M0,96L80,80C160,64,320,32,480,32C640,32,800,64,960,69.3C1120,75,1280,53,1360,42.7L1440,32L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;
