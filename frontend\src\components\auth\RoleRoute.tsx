import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";

interface Props {
  allow: string[]; // roles allowed
}

const RoleRoute = ({ allow }: Props) => {
  const { user } = useAuth();
  const userRoles = user?.roles || [];
  const permitted = allow.some((r) => userRoles.includes(r));
  return permitted ? <Outlet /> : <Navigate to="/dashboard" replace />;
};

export default RoleRoute;

