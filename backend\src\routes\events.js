const express = require("express");
const router = express.Router();
const {
  createEvent,
  getAllEvents,
  getEventBySlug,
  getEventById,
  updateEvent,
  deleteEvent,
  checkSlug,
} = require("../controllers/eventController");
const protect = require("../middleware/auth");
const authorize = require("../middleware/authorize");

// Public routes
router.get("/", getAllEvents);

// Protected routes
router.post("/", protect, authorize("admin", "event_creator"), createEvent);
router.post("/check-slug", protect, checkSlug);

// Dynamic routes (must be last)
router.get("/id/:id", getEventById);
router.get("/:slug", getEventBySlug);
router.put("/:id", protect, authorize("admin", "event_creator"), updateEvent);
router.delete(
  "/:id",
  protect,
  authorize("admin", "event_creator"),
  deleteEvent
);

module.exports = router;
