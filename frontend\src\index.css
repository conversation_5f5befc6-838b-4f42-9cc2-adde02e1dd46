@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 140 65% 15%;

    --card: 0 0% 100%;
    --card-foreground: 140 65% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 140 65% 15%;

    --primary: 140 100% 15%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 140 65% 15%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 51 100% 50%;
    --accent-foreground: 140 65% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 140 100% 15%;

    --islamic-green: 140 100% 15%;
    --islamic-green-light: 140 50% 25%;
    --islamic-gold: 51 100% 50%;
    --islamic-gold-light: 51 100% 85%;

    --gradient-hero: linear-gradient(135deg, hsl(var(--islamic-green)) 0%, hsl(var(--islamic-green-light)) 100%);
    --gradient-section: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  .font-amiri {
    font-family: 'Amiri', serif;
  }
}

/* React Tags Input Styles */
.react-tagsinput {
  @apply bg-background rounded-md border border-input p-2 flex items-center flex-wrap gap-2;
}

.react-tagsinput--focused {
  @apply ring-2 ring-ring ring-offset-2;
}

.react-tagsinput-tag {
  @apply bg-secondary text-secondary-foreground rounded-md px-2 py-1 text-sm flex items-center;
}

.react-tagsinput-remove {
  @apply ml-2 cursor-pointer;
}

.react-tagsinput-input {
  @apply bg-transparent outline-none text-sm;
}