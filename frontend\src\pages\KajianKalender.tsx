import { useEffect, useState } from "react";
import { Calendar as CalendarIcon } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";

interface EventItem {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
  description: string;
}

const KajianKalender = () => {
  const [selected, setSelected] = useState<Date | undefined>(new Date());
  const [events, setEvents] = useState<EventItem[]>([]);
  const [loading, setLoading] = useState(true);

  const start = selected ? format(selected, "yyyy-MM-dd") : undefined;
  const end = selected ? format(selected, "yyyy-MM-dd") : undefined;

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        const url = selected
          ? `http://localhost:3001/api/events?start=${start}&end=${end}`
          : `http://localhost:3001/api/events`;
        const res = await fetch(url);
        const data = await res.json();
        setEvents(data);
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [selected]);

  return (
    <div className="min-h-screen font-inter">
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-10">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
              <CalendarIcon className="h-6 w-6 text-white" />
            </div>
            <h1 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
              Kalender Kajian & Acara
            </h1>
          </div>
          <p className="text-muted-foreground">
            Pilih tanggal untuk melihat kajian pada hari tersebut
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Pilih Tanggal</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selected}
                onSelect={setSelected}
                className="rounded-md border"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                {selected ? `Jadwal ${format(selected, "EEEE, d MMM yyyy")}` : "Semua Jadwal"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div>Loading…</div>
              ) : events.length === 0 ? (
                <div className="text-muted-foreground">Tidak ada event.</div>
              ) : (
                <ul className="space-y-4">
                  {events.map((e) => (
                    <li key={e.id} className="border rounded-md p-4">
                      <div className="font-semibold text-islamic-green">{e.title}</div>
                      <div className="text-sm text-muted-foreground">{e.speaker}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(e.start_time), "HH:mm")} - {format(new Date(e.end_time), "HH:mm")} @ {e.location}
                      </div>
                      <div className="text-sm mt-2">{e.description}</div>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default KajianKalender;

