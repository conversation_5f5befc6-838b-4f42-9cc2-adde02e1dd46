const db = require("../config/db");

const Article = {
  create: async (
    title,
    slug,
    content,
    featured_image_url,
    author_id,
    status = "draft"
  ) => {
    const [result] = await db.query(
      "INSERT INTO articles (title, slug, content, featured_image_url, author_id, status) VALUES (?, ?, ?, ?, ?, ?)",
      [title, slug, content, featured_image_url, author_id, status]
    );
    return result.insertId;
  },

  findAll: async () => {
    const [rows] = await db.query(
      `
      SELECT a.*, u.username as author_name, GROUP_CONCAT(t.name) as tags
      FROM articles a
      JOIN users u ON a.author_id = u.id
      LEFT JOIN article_tags at ON a.id = at.article_id
      LEFT JOIN tags t ON at.tag_id = t.id
      WHERE a.status = 'published'
      GROUP BY a.id
      ORDER BY a.created_at DESC
    `
    );
    rows.forEach((row) => {
      row.tags = row.tags ? row.tags.split(",") : [];
    });
    return rows;
  },

  findById: async (id) => {
    const [rows] = await db.query(
      "SELECT a.*, u.username as author_name FROM articles a JOIN users u ON a.author_id = u.id WHERE a.id = ?",
      [id]
    );
    return rows[0];
  },

  update: async (id, title, slug, content, featured_image_url, status) => {
    const [result] = await db.query(
      "UPDATE articles SET title = ?, slug = ?, content = ?, featured_image_url = ?, status = ? WHERE id = ?",
      [title, slug, content, featured_image_url, status, id]
    );
    return result.affectedRows;
  },

  findBySlug: async (slug) => {
    const [rows] = await db.query(
      `
      SELECT a.*, u.username as author_name, GROUP_CONCAT(t.name) as tags
      FROM articles a
      JOIN users u ON a.author_id = u.id
      LEFT JOIN article_tags at ON a.id = at.article_id
      LEFT JOIN tags t ON at.tag_id = t.id
      WHERE a.slug = ?
      GROUP BY a.id
    `,
      [slug]
    );
    if (rows.length > 0) {
      rows[0].tags = rows[0].tags ? rows[0].tags.split(",") : [];
    }
    return rows[0];
  },

  delete: async (id) => {
    const [result] = await db.query("DELETE FROM articles WHERE id = ?", [id]);
    return result.affectedRows;
  },

  countPublished: async () => {
    const [rows] = await db.query(
      "SELECT COUNT(*) as count FROM articles WHERE status = 'published'"
    );
    return rows[0].count;
  },
};

module.exports = Article;
