# Annabawi Masjid Portal

[![Built with Lovable](https://img.shields.io/badge/Built%20with-Lovable-ff69b4)](https://lovable.dev)
[![Vite](https://img.shields.io/badge/Vite-646CFF?logo=vite&logoColor=white)](https://vitejs.dev)
[![React](https://img.shields.io/badge/React-61DAFB?logo=react&logoColor=black)](https://reactjs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-3178C6?logo=typescript&logoColor=white)](https://typescriptlang.org)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-06B6D4?logo=tailwindcss&logoColor=white)](https://tailwindcss.com)

**URL**: https://lovable.dev/projects/064f353f-780d-4a85-9453-************

## 📝 Project Overview

Annabawi Masjid Portal is a modern Islamic center website designed to serve the Muslim community with comprehensive features including prayer schedules, Islamic study sessions (kajian), articles, and community engagement tools. The website is built with modern web technologies and follows Islamic design principles with a green and gold color scheme.

## 🎯 Features

- **🕋 Prayer Schedule (Jadwal Sholat)**: Real-time prayer times with automatic location detection
- **📅 Islamic Studies Schedule (Kajian)**: Regular Islamic study sessions and events
- **📰 Islamic Articles**: Educational content and religious guidance
- **📱 Mobile App Download**: Links to mobile applications for enhanced user experience
- **📍 Contact & Location**: Masjid location, contact information, and Google Maps integration
- **🎨 Islamic Design**: Beautiful UI with Islamic geometric patterns and colors
- **📱 Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **⚡ Fast Performance**: Built with Vite for optimal loading speeds

## 🛠 Technology Stack

### Frontend Framework

- **React 18.3.1** - Modern JavaScript library for building user interfaces
- **TypeScript 5.8.3** - Type-safe JavaScript for better development experience
- **Vite 5.4.19** - Next-generation frontend build tool

### Styling & UI

- **Tailwind CSS 3.4.17** - Utility-first CSS framework
- **shadcn/ui** - Re-usable components built with Radix UI and Tailwind CSS
- **Radix UI** - Low-level UI primitives for accessible components
- **Tailwind CSS Animate** - Animation utilities for Tailwind CSS
- **Class Variance Authority (CVA)** - For component variant management
- **Lucide React** - Beautiful & consistent icon pack

### State Management & Data

- **TanStack React Query 5.83.0** - Powerful data synchronization for React
- **React Hook Form 7.61.1** - Performant forms with easy validation
- **Zod 3.25.76** - TypeScript-first schema validation

### Routing & Navigation

- **React Router DOM 6.30.1** - Declarative routing for React

### Development Tools

- **ESLint 9.32.0** - JavaScript/TypeScript linting utility
- **TypeScript ESLint 8.38.0** - TypeScript-specific ESLint rules
- **PostCSS 8.5.6** - Tool for transforming CSS with JavaScript
- **Autoprefixer 10.4.21** - Automatic vendor prefixes for CSS

### Build & Development

- **@vitejs/plugin-react-swc** - Fast React refresh with SWC
- **Lovable Tagger** - Build tagging for Lovable platform integration

### Additional Libraries

- **Date-fns 3.6.0** - Modern JavaScript date utility library
- **React Resizable Panels 2.1.9** - Resizable panel components
- **Embla Carousel React 8.6.0** - Lightweight carousel library
- **Recharts 2.15.4** - Composable charting library for React
- **Sonner 1.7.4** - Toast notifications
- **Vaul 0.9.9** - Drawer component for mobile

### Design System

```typescript
// Custom Islamic Color Palette
colors: {
  'islamic-green': {
    DEFAULT: 'hsl(140 100% 15%)',    // Deep green
    light: 'hsl(140 50% 25%)'        // Lighter green
  },
  'islamic-gold': {
    DEFAULT: 'hsl(51 100% 50%)',     // Bright gold
    light: 'hsl(51 100% 85%)'        // Light gold
  }
}
```

### Custom Features

- **Islamic Button Variants**:
  - `islamic`: Gold background with green text
  - `islamic-outline`: Green border with hover effects
- **Islamic Color Scheme**: Green and gold theme following Islamic design principles
- **Arabic Typography Support**: Proper rendering for Arabic text and Islamic content
- **Prayer Time Integration**: Dynamic prayer schedule with location-based timing
- **Responsive Islamic Patterns**: CSS-based geometric patterns for visual enhancement

## 🏗 Project Structure

```
src/
├── components/           # React components
│   ├── ui/              # Reusable UI components (shadcn/ui)
│   ├── HeroSection.tsx  # Landing page hero section
│   ├── Navigation.tsx   # Main navigation component
│   ├── JadwalSholatSection.tsx  # Prayer schedule section
│   ├── KajianSection.tsx        # Islamic studies section
│   ├── ArtikelSection.tsx       # Articles section
│   ├── DownloadAppSection.tsx   # Mobile app section
│   ├── KontakSection.tsx        # Contact section
│   └── Footer.tsx       # Footer component
├── pages/               # Page components
│   ├── Index.tsx        # Home page
│   ├── LandingPage.tsx  # Main landing page
│   └── NotFound.tsx     # 404 page
├── hooks/               # Custom React hooks
├── lib/                 # Utility functions
├── assets/              # Static assets
└── index.css           # Global styles with CSS variables
```

## 🎨 Design Principles

- **Islamic Aesthetics**: Green and gold color scheme reflecting Islamic tradition
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation
- **Performance**: Optimized images, lazy loading, and minimal bundle size
- **Mobile-First**: Responsive design ensuring great experience on all devices
- **Typography**: Google Fonts integration with Poppins, Inter, and Montserrat
- **Animations**: Smooth transitions and hover effects for enhanced UX

## 🚀 Getting Started

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher) - [Install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- **npm** or **yarn** package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <YOUR_GIT_URL>
   cd annabawi-masjid-portal
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Build for production**

   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run build:dev` - Build in development mode
- `npm run lint` - Run ESLint for code quality
- `npm run preview` - Preview production build locally

## 🎛 Configuration

### Environment Setup

The project uses Vite's environment variables. Create a `.env` file for local configuration:

```env
# Add your environment variables here
VITE_API_URL=your_api_url_here
VITE_GOOGLE_MAPS_API_KEY=your_maps_api_key
```

### Tailwind Configuration

Custom Islamic design system configured in `tailwind.config.ts`:

```typescript
theme: {
  extend: {
    colors: {
      'islamic-green': {
        DEFAULT: 'hsl(var(--islamic-green))',
        light: 'hsl(var(--islamic-green-light))'
      },
      'islamic-gold': {
        DEFAULT: 'hsl(var(--islamic-gold))',
        light: 'hsl(var(--islamic-gold-light))'
      }
    },
    fontFamily: {
      poppins: ['Poppins', 'sans-serif'],
      inter: ['Inter', 'sans-serif'],
      montserrat: ['Montserrat', 'sans-serif']
    }
  }
}
```

## 🔧 Development Guide

### Component Structure

- All components follow TypeScript best practices
- UI components are built with shadcn/ui and Radix UI
- Islamic-themed button variants available
- Responsive design with mobile-first approach

### Adding New Components

1. Create component in appropriate directory
2. Use TypeScript interfaces for props
3. Follow naming conventions (PascalCase)
4. Add proper JSDoc comments
5. Export from index files

### Styling Guidelines

- Use Tailwind CSS utility classes
- Custom Islamic colors: `islamic-green`, `islamic-gold`
- Responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`
- Component variants with CVA

## 📱 Deployment

### Lovable Platform

Simply open [Lovable](https://lovable.dev/projects/064f353f-780d-4a85-9453-************) and click on **Share → Publish**.

### Custom Domain

To connect a custom domain:

1. Navigate to **Project > Settings > Domains**
2. Click **Connect Domain**
3. Follow the setup instructions

Read more: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

### Manual Deployment

For other hosting platforms:

```bash
npm run build
# Deploy the dist/ folder to your hosting provider
```

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open Pull Request**

### Code Standards

- **TypeScript**: Strict type checking enabled
- **ESLint**: Follow the configured rules
- **Prettier**: Code formatting (if configured)
- **Commit Messages**: Use conventional commit format

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Lovable Platform** - For the development environment
- **shadcn/ui** - For the beautiful component library
- **Radix UI** - For accessible primitives
- **Tailwind CSS** - For the utility-first CSS framework
- **React Community** - For the amazing ecosystem

## 📞 Support & Contact

- **Website**: [Annabawi Masjid Portal](https://your-domain.com)
- **Email**: <EMAIL>
- **WhatsApp**: [Join our channel](https://www.whatsapp.com/channel/0029VaTTWVAGufIryonAMo1z)
- **Social Media**:
  - Instagram: [@annabawitv](https://www.instagram.com/annabawitv/)
  - Facebook: [An Nabawi](https://facebook.com/annabawi)

---

**Built with ❤️ for the Muslim Ummah**

_"And whoever builds a mosque for Allah, Allah will build for him a house in Paradise."_ - Hadith
