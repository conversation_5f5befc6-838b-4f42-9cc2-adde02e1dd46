import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import ScrollToTop from "./components/ScrollToTop";

// Layouts
import DashboardLayout from "./components/layout/DashboardLayout";

// Auth
import Login from "./pages/Login";
import Register from "./pages/Register";
import PrivateRoute from "./components/auth/PrivateRoute";
import RoleRoute from "./components/auth/RoleRoute";

// Public Pages
import Index from "./pages/Index";
import TentangKami from "./pages/TentangKami";
import ArticleDetail from "./pages/ArticleDetail";
import Kajian<PERSON>alender from "./pages/KajianKalender";
import Ziswaf from "./pages/Ziswaf";
import Laporan<PERSON>euangan from "./pages/LaporanKeuangan";
import NotFound from "./pages/NotFound";

// Dashboard Pages
import Dashboard from "./pages/Dashboard";
import Settings from "./pages/Settings";
import UserManagement from "./pages/users/UserManagement";
import ArticleList from "./pages/articles/ArticleList";
import NewArticle from "./pages/articles/NewArticle";
import EditArticle from "./pages/articles/EditArticle";
import EventList from "./pages/events/EventList";
import NewEvent from "./pages/events/NewEvent";
import EditEvent from "./pages/events/EditEvent";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Index />} />
            <Route path="/articles/:slug" element={<ArticleDetail />} />
            <Route path="/tentang-kami" element={<TentangKami />} />
            <Route path="/kajian-kalender" element={<KajianKalender />} />
            <Route path="/ziswaf" element={<Ziswaf />} />
            <Route path="/laporan-keuangan" element={<LaporanKeuangan />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Private Dashboard Routes */}
            <Route path="/dashboard" element={<PrivateRoute />}>
              <Route element={<DashboardLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="settings" element={<Settings />} />
                <Route element={<RoleRoute allow={["admin"]} />}>
                  <Route path="users" element={<UserManagement />} />
                </Route>
                <Route
                  element={<RoleRoute allow={["admin", "article_creator"]} />}
                >
                  <Route path="articles" element={<ArticleList />} />
                  <Route path="articles/new" element={<NewArticle />} />
                  <Route path="articles/edit/:id" element={<EditArticle />} />
                </Route>
                <Route
                  element={<RoleRoute allow={["admin", "event_creator"]} />}
                >
                  <Route path="events" element={<EventList />} />
                  <Route path="events/new" element={<NewEvent />} />
                  <Route path="events/edit/:id" element={<EditEvent />} />
                </Route>
                {/* Add other dashboard routes here */}
              </Route>
            </Route>

            {/* Catch-all Not Found Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
