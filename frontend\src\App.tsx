import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import ScrollToTop from "./components/ScrollToTop";

// Layouts
import DashboardLayout from "./components/layout/DashboardLayout";

// Auth
import Login from "./pages/Login";
import Register from "./pages/Register";
import PrivateRoute from "./components/auth/PrivateRoute";

// Public Pages
import Index from "./pages/Index";
import ArticleDetail from "./pages/ArticleDetail";
import NotFound from "./pages/NotFound";

// Dashboard Pages
import Dashboard from "./pages/Dashboard";
import Settings from "./pages/Settings";
import UserManagement from "./pages/users/UserManagement";
import ArticleList from "./pages/articles/ArticleList";
import NewArticle from "./pages/articles/NewArticle";
import EditArticle from "./pages/articles/EditArticle";
import EventList from "./pages/events/EventList";
import NewEvent from "./pages/events/NewEvent";
import EditEvent from "./pages/events/EditEvent";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Index />} />
            <Route path="/articles/:slug" element={<ArticleDetail />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Private Dashboard Routes */}
            <Route path="/dashboard" element={<PrivateRoute />}>
              <Route element={<DashboardLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="settings" element={<Settings />} />
                <Route path="users" element={<UserManagement />} />
                <Route path="articles" element={<ArticleList />} />
                <Route path="articles/new" element={<NewArticle />} />
                <Route path="articles/edit/:id" element={<EditArticle />} />
                <Route path="events" element={<EventList />} />
                <Route path="events/new" element={<NewEvent />} />
                <Route path="events/edit/:id" element={<EditEvent />} />
                {/* Add other dashboard routes here */}
              </Route>
            </Route>

            {/* Catch-all Not Found Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
