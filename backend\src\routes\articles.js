const express = require("express");
const router = express.Router();
const auth = require("../middleware/auth");
const authorize = require("../middleware/authorize");
const {
  createArticle,
  getAllArticles,
  getArticleById,
  updateArticle,
  deleteArticle,
  getArticleBySlug,
  checkSlug,
} = require("../controllers/articleController");

// @route   POST api/articles/check-slug
// @desc    Check if slug exists
// @access  Private
router.post("/check-slug", auth, checkSlug);

// @route   POST api/articles
// @desc    Create an article
// @access  Private (admin, article_creator)
router.post(
  "/",
  [auth, authorize(["admin", "article_creator"])],
  createArticle
);

// @route   GET api/articles
// @desc    Get all articles
// @access  Public
router.get("/", getAllArticles);

// @route   GET api/articles/slug/:slug
// @desc    Get single article by slug
// @access  Public
router.get("/slug/:slug", getArticleBySlug);

// @route   GET api/articles/:id
// @desc    Get single article
// @access  Public
router.get("/:id", getArticleById);

// @route   GET api/articles/:id/tags
// @desc    Get tags for a single article
// @access  Private
router.get("/:id/tags", auth, (req, res) => {
  const Tag = require("../models/Tag");
  Tag.getTagsByArticleId(req.params.id)
    .then((tags) => res.json(tags))
    .catch((err) => res.status(500).json({ message: "Server error", err }));
});

// @route   PUT api/articles/:id
// @desc    Update an article
// @access  Private (owner, admin)
router.put("/:id", auth, updateArticle);

// @route   DELETE api/articles/:id
// @desc    Delete an article
// @access  Private (owner, admin)
router.delete("/:id", auth, deleteArticle);

module.exports = router;
