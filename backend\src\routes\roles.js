const express = require("express");
const router = express.Router();
const roleController = require("../controllers/roleController");
const auth = require("../middleware/auth");
const authorize = require("../middleware/authorize");

// @route   GET api/roles
// @desc    Get all roles
// @access  Private (Admin)
router.get("/", [auth, authorize(["admin"])], roleController.getAllRoles);

module.exports = router;
