import { useState, useEffect } from "react";

export interface PrayerSchedule {
  tanggal: string;
  imsak: string;
  subuh: string;
  terbit: string;
  dhuha: string;
  dzuhur: string;
  ashar: string;
  maghrib: string;
  isya: string;
  date: string;
}

export interface PrayerData {
  id: number;
  lokasi: string;
  daerah: string;
  jadwal: PrayerSchedule;
  jadwal_tomorrow?: PrayerSchedule;
}

interface PrayerTimesState {
  data: PrayerData | null;
  loading: boolean;
  error: string | null;
}

const usePrayerTimes = (cityId: string | null) => {
  const [prayerTimes, setPrayerTimes] = useState<PrayerTimesState>({
    data: null,
    loading: false,
    error: null,
  });

  useEffect(() => {
    if (!cityId) {
      setPrayerTimes({ data: null, loading: false, error: null });
      return;
    }

    const fetchPrayerData = async () => {
      setPrayerTimes({ data: null, loading: true, error: null });
      try {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, "0");
        const day = String(today.getDate()).padStart(2, "0");

        const prayerResponse = await fetch(
          `https://api.myquran.com/v2/sholat/jadwal/${cityId}/${year}/${month}/${day}`
        );
        if (!prayerResponse.ok)
          throw new Error(
            `Failed to fetch prayer times: ${prayerResponse.statusText}`
          );
        const prayerData = await prayerResponse.json();
        if (!prayerData.status)
          throw new Error(
            `API error for prayer times: ${
              prayerData.message || "Unknown error"
            }`
          );

        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        const tomorrowYear = tomorrow.getFullYear();
        const tomorrowMonth = String(tomorrow.getMonth() + 1).padStart(2, "0");
        const tomorrowDay = String(tomorrow.getDate()).padStart(2, "0");

        const tomorrowPrayerResponse = await fetch(
          `https://api.myquran.com/v2/sholat/jadwal/${cityId}/${tomorrowYear}/${tomorrowMonth}/${tomorrowDay}`
        );
        const tomorrowPrayerData = await tomorrowPrayerResponse.json();

        const finalData = {
          ...prayerData.data,
          jadwal_tomorrow: tomorrowPrayerData.data.jadwal,
        };

        setPrayerTimes({ data: finalData, loading: false, error: null });
      } catch (err) {
        if (err instanceof Error) {
          setPrayerTimes({ data: null, loading: false, error: err.message });
        } else {
          setPrayerTimes({
            data: null,
            loading: false,
            error: "An unknown error occurred.",
          });
        }
      }
    };

    fetchPrayerData();
  }, [cityId]);

  return prayerTimes;
};

export default usePrayerTimes;
