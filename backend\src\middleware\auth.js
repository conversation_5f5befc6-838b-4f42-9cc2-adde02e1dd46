const jwt = require("jsonwebtoken");
const JWT_SECRET = process.env.JWT_SECRET || "your-default-secret-key";

module.exports = function (req, res, next) {
  let token;
  // Get token from header
  const authHeader = req.header("Authorization");

  if (authHeader && authHeader.startsWith("Bearer ")) {
    token = authHeader.split(" ")[1];
  } else if (req.query.token) {
    // Fallback for CKEditor upload
    token = req.query.token;
  }

  // Check if not token
  if (!token) {
    return res.status(401).json({ message: "No token, authorization denied" });
  }

  // Verify token
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded.user;
    next();
  } catch (err) {
    res.status(401).json({ message: "Token is not valid" });
  }
};
