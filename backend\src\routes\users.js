const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const auth = require("../middleware/auth");
const authorize = require("../middleware/authorize");

// @route   GET api/users
// @desc    Get all users
// @access  Private (Admin)
router.get("/", [auth, authorize(["admin"])], userController.getAllUsers);

// @route   POST api/users
// @desc    Create a user
// @access  Private (Admin)
router.post("/", [auth, authorize(["admin"])], userController.createUser);

// @route   PUT api/users/:id
// @desc    Update a user
// @access  Private (Admin or self)
router.put("/:id", auth, userController.updateUser);

// @route   DELETE api/users/:id
// @desc    Delete a user
// @access  Private (Admin)
router.delete("/:id", [auth, authorize(["admin"])], userController.deleteUser);

module.exports = router;
