import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/sonner";

const Ziswaf = () => {
  const [form, setForm] = useState({
    type: "INFAQ",
    amount: "",
    name: "",
    email: "",
    phone: "",
    message: "",
  });
  const [loading, setLoading] = useState(false);

  const submit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      const res = await fetch("http://localhost:3001/api/ziswaf/donations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: form.type,
          amount: Number(form.amount),
          name: form.name || undefined,
          email: form.email || undefined,
          phone: form.phone || undefined,
          message: form.message || undefined,
        }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.message || "Gagal mengirim donasi");
      toast("Terima kasih!", { description: "Donasi tercatat (dummy)." });
      setForm({ type: "INFAQ", amount: "", name: "", email: "", phone: "", message: "" });
    } catch (err: any) {
      toast("Error", { description: err.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen font-inter">
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <div className="mb-10">
          <h1 className="font-poppins text-3xl text-islamic-green font-bold">ZISWAF</h1>
          <p className="text-muted-foreground mt-2">
            ZISWAF adalah singkatan dari Zakat, Infaq, Sedekah, dan Wakaf. Berikut penjelasan singkat:
          </p>
          <ul className="list-disc pl-6 mt-2 text-muted-foreground">
            <li><b>Zakat</b>: kewajiban bagi Muslim yang memenuhi nisab untuk menyalurkan sebagian harta.</li>
            <li><b>Infaq</b>: mengeluarkan harta untuk kebaikan, tidak terikat jumlah/nisab.</li>
            <li><b>Sedekah</b>: pemberian sukarela, bisa materi atau non-materi.</li>
            <li><b>Wakaf</b>: menahan harta pokok untuk kemaslahatan, hasil/manfaatnya digunakan untuk umum.</li>
          </ul>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Form Donasi</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={submit} className="space-y-4">
                <div>
                  <Label>Jenis</Label>
                  <select
                    className="w-full border rounded-md p-2"
                    value={form.type}
                    onChange={(e) => setForm({ ...form, type: e.target.value })}
                  >
                    <option value="ZAKAT">Zakat</option>
                    <option value="INFAQ">Infaq</option>
                    <option value="SEDEKAH">Sedekah</option>
                    <option value="WAKAF">Wakaf</option>
                  </select>
                </div>
                <div>
                  <Label>Nominal</Label>
                  <Input type="number" value={form.amount} onChange={(e) => setForm({ ...form, amount: e.target.value })} required />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Nama (opsional jika login)</Label>
                    <Input value={form.name} onChange={(e) => setForm({ ...form, name: e.target.value })} />
                  </div>
                  <div>
                    <Label>Email (opsional jika login)</Label>
                    <Input type="email" value={form.email} onChange={(e) => setForm({ ...form, email: e.target.value })} />
                  </div>
                  <div>
                    <Label>No HP (opsional jika login)</Label>
                    <Input value={form.phone} onChange={(e) => setForm({ ...form, phone: e.target.value })} />
                  </div>
                  <div>
                    <Label>Pesan</Label>
                    <Input value={form.message} onChange={(e) => setForm({ ...form, message: e.target.value })} />
                  </div>
                </div>
                <Button type="submit" disabled={loading}>{loading ? "Mengirim..." : "Kirim Donasi"}</Button>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Riwayat Guest (Cek via Email + HP)</CardTitle>
            </CardHeader>
            <CardContent>
              <GuestHistoryWidget />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const GuestHistoryWidget = () => {
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [rows, setRows] = useState<any[]>([]);

  const load = async () => {
    const res = await fetch(`http://localhost:3001/api/ziswaf/guest/history?email=${encodeURIComponent(email)}&phone=${encodeURIComponent(phone)}`);
    const data = await res.json();
    setRows(Array.isArray(data) ? data : []);
  };

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <Input placeholder="Email" value={email} onChange={(e) => setEmail(e.target.value)} />
        <Input placeholder="No HP" value={phone} onChange={(e) => setPhone(e.target.value)} />
      </div>
      <Button onClick={load}>Cek Riwayat</Button>
      <div className="space-y-2">
        {rows.length === 0 ? (
          <div className="text-muted-foreground text-sm">Belum ada data.</div>
        ) : (
          rows.map((r) => (
            <div key={r.id} className="border rounded-md p-3 text-sm">
              <div><b>{r.type}</b> - Rp{Number(r.amount).toLocaleString()}</div>
              <div className="text-muted-foreground">{new Date(r.created_at).toLocaleString()}</div>
              {r.message && <div className="mt-1">“{r.message}”</div>}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Ziswaf;

