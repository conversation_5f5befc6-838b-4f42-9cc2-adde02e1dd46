import { Book<PERSON><PERSON>, Calendar, ArrowRight, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";

interface Article {
  id: number;
  title: string;
  slug: string;
  content: string;
  featured_image_url: string;
  author_name: string;
  created_at: string;
  tags: string[];
}

interface ArtikelSectionProps {
  articles: Article[];
  isLoading: boolean;
}

const ArtikelSection = ({ articles, isLoading }: ArtikelSectionProps) => {
  const colorPalette = [
    "bg-blue-100 text-blue-800",
    "bg-green-100 text-green-800",
    "bg-yellow-100 text-yellow-800",
    "bg-purple-100 text-purple-800",
    "bg-pink-100 text-pink-800",
    "bg-indigo-100 text-indigo-800",
    "bg-red-100 text-red-800",
  ];

  const getCategoryColor = (category: string) => {
    // Create a simple hash from the category name to get a consistent color
    let hash = 0;
    for (let i = 0; i < category.length; i++) {
      hash = category.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash % colorPalette.length);
    return colorPalette[index];
  };

  const extractExcerpt = (html: string, length = 150) => {
    const text = html.replace(/<[^>]+>/g, "");
    return text.length > length ? text.substring(0, length) + "..." : text;
  };

  const extractFirstImage = (html: string | null | undefined) => {
    if (!html) return null;
    const imgTag = html.match(/<img[^>]+src="([^">]+)"/);
    return imgTag ? imgTag[1] : null;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <section id="artikel" className="py-20 bg-gradient-section">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <h2 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
              Artikel Terbaru
            </h2>
          </div>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Baca artikel-artikel inspiratif dan kajian Islam yang mendalam dari
            para ustadz
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          {isLoading && <p>Loading articles...</p>}
          {!isLoading && articles.length > 0 && (
            <>
              {/* Featured Article */}
              <div className="mb-12">
                <Card className="overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="md:flex">
                    <div className="md:w-1/2">
                      <img
                        src={
                          articles[0].featured_image_url ||
                          extractFirstImage(articles[0].content) ||
                          "/placeholder.svg"
                        }
                        alt={articles[0].title}
                        className="w-full h-64 md:h-full object-cover"
                      />
                    </div>
                    <div className="md:w-1/2 p-8">
                      {articles[0].tags && articles[0].tags.length > 0 && (
                        <Badge
                          className={`${getCategoryColor(
                            articles[0].tags[0]
                          )} mb-2`}
                        >
                          {articles[0].tags[0]}
                        </Badge>
                      )}
                      <h3 className="font-poppins font-bold text-2xl text-islamic-green mb-4 leading-tight">
                        {articles[0].title}
                      </h3>
                      <p className="text-muted-foreground mb-6 leading-relaxed">
                        {extractExcerpt(articles[0].content)}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-6">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(articles[0].created_at)}</span>
                        </div>
                      </div>
                      <Button
                        asChild
                        variant="islamic"
                        className="w-full sm:w-auto"
                      >
                        <Link to={`/articles/${articles[0].slug}`}>
                          Baca Selengkapnya
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Article Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                {articles.slice(1, 4).map((article) => (
                  <Card
                    key={article.id}
                    className="overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={
                          article.featured_image_url ||
                          extractFirstImage(article.content) ||
                          "/placeholder.svg"
                        }
                        alt={article.title}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                      />
                    </div>
                    <CardContent className="p-4">
                      {article.tags && article.tags.length > 0 && (
                        <Badge
                          className={`${getCategoryColor(
                            article.tags[0]
                          )} mb-2`}
                        >
                          {article.tags[0]}
                        </Badge>
                      )}
                      <h3 className="font-poppins font-semibold text-lg text-islamic-green mb-3 leading-tight line-clamp-2">
                        {article.title}
                      </h3>
                      <p className="text-muted-foreground text-sm mb-4 leading-relaxed line-clamp-3">
                        {extractExcerpt(article.content)}
                      </p>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(article.created_at)}</span>
                        </div>
                      </div>
                      <Button
                        asChild
                        variant="islamic-outline"
                        size="sm"
                        className="w-full"
                      >
                        <Link to={`/articles/${article.slug}`}>
                          Baca Artikel
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
};

export default ArtikelSection;
