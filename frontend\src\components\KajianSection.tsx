import { Calendar, Clock, MapPin, User, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

interface Event {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
  description: string;
  tags: string[];
}

interface KajianSectionProps {
  events: Event[];
  isLoading: boolean;
}

const KajianSection = ({ events, isLoading }: KajianSectionProps) => {
  const colorPalette = [
    "bg-blue-100 text-blue-800",
    "bg-green-100 text-green-800",
    "bg-yellow-100 text-yellow-800",
    "bg-purple-100 text-purple-800",
    "bg-pink-100 text-pink-800",
    "bg-indigo-100 text-indigo-800",
    "bg-red-100 text-red-800",
  ];

  const getCategoryColor = (category: string) => {
    // Create a simple hash from the category name to get a consistent color
    let hash = 0;
    for (let i = 0; i < category.length; i++) {
      hash = category.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash % colorPalette.length);
    return colorPalette[index];
  };

  const renderSkeletons = () =>
    Array.from({ length: 4 }).map((_, index) => (
      <Card key={index}>
        <CardHeader>
          <Skeleton className="h-5 w-1/4 mb-2" />
          <Skeleton className="h-6 w-3/4" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-5 w-1/2" />
          <Skeleton className="h-5 w-2/3" />
          <Skeleton className="h-5 w-1/3" />
          <Skeleton className="h-5 w-3/4" />
          <Skeleton className="h-8 w-full mt-4" />
        </CardContent>
      </Card>
    ));

  return (
    <section id="kajian" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <h2 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
              Jadwal Kajian & Acara
            </h2>
          </div>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Ikuti berbagai kajian ilmu dan acara Islami yang diselenggarakan
            secara rutin
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12">
            {isLoading
              ? renderSkeletons()
              : events.map((event) => (
                  <Card
                    key={event.id}
                    className="hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          {event.tags && event.tags.length > 0 && (
                            <Badge
                              className={`${getCategoryColor(
                                event.tags[0]
                              )} mb-2`}
                            >
                              {event.tags[0]}
                            </Badge>
                          )}
                          <CardTitle className="font-poppins text-xl text-islamic-green leading-tight">
                            {event.title}
                          </CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <User className="h-4 w-4" />
                        <span className="font-medium">{event.speaker}</span>
                      </div>

                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {format(
                            new Date(event.start_time),
                            "EEEE, d MMMM yyyy"
                          )}
                        </span>
                      </div>

                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>
                          {format(new Date(event.start_time), "HH:mm")} -{" "}
                          {format(new Date(event.end_time), "HH:mm")}
                        </span>
                      </div>

                      <div className="flex items-center gap-2 text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{event.location}</span>
                      </div>

                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {event.description}
                      </p>

                      <Button
                        asChild
                        variant="islamic-outline"
                        size="sm"
                        className="w-full mt-4"
                      >
                        <a href="#">
                          Daftar Sekarang
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </a>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <Card className="max-w-2xl mx-auto bg-gradient-to-r from-islamic-green to-islamic-green-light text-white">
              <CardContent className="py-8">
                <h3 className="font-poppins font-bold text-2xl mb-4">
                  Ingin Mengikuti Semua Kajian?
                </h3>
                <p className="text-white/90 mb-6">
                  Bergabunglah dengan grup WhatsApp kami untuk mendapatkan
                  update lengkap tentang semua kajian dan acara
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button variant="islamic" size="lg">
                    Lihat Semua Acara
                  </Button>
                  <Button
                    variant="islamic-outline"
                    size="lg"
                    className="bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-islamic-green"
                  >
                    Gabung WhatsApp Group
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default KajianSection;
