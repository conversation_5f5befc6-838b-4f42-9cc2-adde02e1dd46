const express = require("express");
const router = express.Router();
const auth = require("../middleware/auth");
const {
  createDonation,
  getMyHistory,
  getGuestHistory,
  getFinanceSummary,
} = require("../controllers/ziswafController");

// Public: guest donation (or logged-in user; auth optional)
router.post("/donations", createDonation);
// Authenticated: donation linked to logged-in user
router.post("/donations/auth", auth, createDonation);

// Authenticated: my history
router.get("/me/history", auth, getMyHistory);

// Public: guest history via email+phone
router.get("/guest/history", getGuestHistory);

// Public (dummy): finance summary (can later restrict to admin)
router.get("/reports/finance/summary", getFinanceSummary);

module.exports = router;
