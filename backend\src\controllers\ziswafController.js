const Ziswaf = require("../models/Ziswaf");

exports.createDonation = async (req, res) => {
  try {
    const { type, amount, message, name, email, phone } = req.body;
    // If user is logged in, link to user_id, else use guest fields
    const user_id = req.user ? req.user.id : null;
    const donationId = await Ziswaf.createDonation({
      user_id,
      guest_name: user_id ? null : name,
      guest_email: user_id ? null : email,
      guest_phone: user_id ? null : phone,
      type,
      amount,
      message,
    });
    res.status(201).json({ message: "Donation recorded (dummy)", donationId });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.getMyHistory = async (req, res) => {
  try {
    const rows = await Ziswaf.getUserDonations(req.user.id);
    res.json(rows);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.getGuestHistory = async (req, res) => {
  try {
    const { email, phone } = req.query;
    if (!email || !phone) return res.status(400).json({ message: "email & phone required" });
    const rows = await Ziswaf.getGuestDonations({ email, phone });
    res.json(rows);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

exports.getFinanceSummary = async (req, res) => {
  try {
    const payload = await Ziswaf.getFinanceSummary();
    res.json(payload);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

