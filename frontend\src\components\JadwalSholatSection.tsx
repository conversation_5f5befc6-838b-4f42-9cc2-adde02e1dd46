import { MapPin } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PrayerSchedule, PrayerData } from "@/hooks/usePrayerTimes";
import { Skeleton } from "@/components/ui/skeleton";
import { CitySearch } from "./CitySearch";

interface JadwalSholatSectionProps {
  prayerTimes: {
    data: PrayerData | null;
    loading: boolean;
    error: string | null;
  };
  locationError: string | null;
  onCitySelect: (cityId: string) => void;
}

const JadwalSholatSection = ({
  prayerTimes,
  locationError,
  onCitySelect,
}: JadwalSholatSectionProps) => {
  const { data, loading, error } = prayerTimes;

  const prayerNames = ["Subuh", "Dzuhur", "Ashar", "Maghrib", "Isya"];

  const renderPrayerTimes = (schedule: PrayerSchedule, isToday: boolean) => {
    if (!schedule) return null;
    const times = [
      schedule.subuh,
      schedule.dzuhur,
      schedule.ashar,
      schedule.maghrib,
      schedule.isya,
    ];

    let currentPrayerIndex = -1;
    if (isToday) {
      const now = new Date();
      const currentTime = `${String(now.getHours()).padStart(2, "0")}:${String(
        now.getMinutes()
      ).padStart(2, "0")}`;
      for (let i = times.length - 1; i >= 0; i--) {
        if (currentTime >= times[i]) {
          currentPrayerIndex = i;
          break;
        }
      }
    }

    return times.map((time, index) => (
      <div
        key={index}
        className={`font-bold text-center text-lg ${
          isToday && index === currentPrayerIndex
            ? "text-islamic-green scale-110"
            : "text-gray-800"
        }`}
      >
        {time}
      </div>
    ));
  };

  const renderSkeleton = () => (
    <div className="space-y-4">
      <Skeleton className="h-8 w-3/4" />
      <div className="grid grid-cols-6 gap-4">
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
      </div>
      <div className="grid grid-cols-6 gap-4">
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
      </div>
    </div>
  );

  const renderContent = () => {
    if (loading) {
      return renderSkeleton();
    }

    if (error || locationError || !data) {
      return (
        <div className="text-center">
          <p className="text-red-500 mb-4">{error || locationError}</p>
          <p className="mb-2 text-gray-600">
            Silakan pilih kota Anda secara manual:
          </p>
          <CitySearch onCitySelect={onCitySelect} />
        </div>
      );
    }

    return (
      <>
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-islamic-green" />
            <span className="font-semibold text-lg text-gray-700">
              {data.lokasi}, {data.daerah}
            </span>
          </div>
          <p className="text-sm text-gray-500 mt-2 sm:mt-0">
            {data.jadwal.tanggal}
          </p>
        </div>

        {/* Khutbah Jumat */}
        <div className="bg-islamic-green/10 p-4 rounded-lg mb-6 flex items-center gap-4">
          <Avatar className="h-12 w-12 border-2 border-islamic-green">
            <AvatarImage
              src="https://github.com/shadcn.png"
              alt="Dr. Adi Hidayat"
            />
            <AvatarFallback>AH</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold text-gray-800">
              Dr. Adi Hidayat, Lc., M.A.
            </p>
            <p className="text-sm text-gray-600">
              Founder Quantum Akhyar Institute
            </p>
          </div>
        </div>

        {/* Prayer Times Section */}
        <div className="mt-6">
          {/* Mobile View - List */}
          <div className="md:hidden">
            <h3 className="font-semibold text-gray-700 mb-2">
              Hari Ini ({data.jadwal.date})
            </h3>
            <div className="space-y-2">
              {prayerNames.map((name, index) => {
                const schedule = data.jadwal;
                const times = [
                  schedule.subuh,
                  schedule.dzuhur,
                  schedule.ashar,
                  schedule.maghrib,
                  schedule.isya,
                ];
                return (
                  <div
                    key={name}
                    className="flex justify-between items-center bg-gray-50 p-3 rounded-lg"
                  >
                    <span className="font-medium text-gray-600">{name}</span>
                    <span
                      className={`font-bold text-lg ${
                        renderPrayerTimes(schedule, true)[index].props.className
                      }`}
                    >
                      {times[index]}
                    </span>
                  </div>
                );
              })}
            </div>

            {data.jadwal_tomorrow && (
              <div className="mt-4">
                <h3 className="font-semibold text-gray-700 mb-2">
                  Besok ({data.jadwal_tomorrow.date})
                </h3>
                <div className="space-y-2">
                  {prayerNames.map((name, index) => {
                    const schedule = data.jadwal_tomorrow;
                    const times = [
                      schedule.subuh,
                      schedule.dzuhur,
                      schedule.ashar,
                      schedule.maghrib,
                      schedule.isya,
                    ];
                    return (
                      <div
                        key={name}
                        className="flex justify-between items-center bg-gray-50 p-3 rounded-lg"
                      >
                        <span className="font-medium text-gray-600">
                          {name}
                        </span>
                        <span className="font-bold text-lg text-gray-800">
                          {times[index]}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Desktop View - Table */}
          <div className="hidden md:block overflow-x-auto">
            <div className="min-w-full">
              {/* Table Header */}
              <div className="grid grid-cols-6 gap-4 pb-2 border-b border-gray-200">
                <div className="font-semibold text-gray-500 text-sm uppercase tracking-wider">
                  Tanggal
                </div>
                {prayerNames.map((name) => (
                  <div
                    key={name}
                    className="font-semibold text-gray-500 text-sm uppercase tracking-wider text-center"
                  >
                    {name}
                  </div>
                ))}
              </div>

              {/* Today's Times */}
              <div className="grid grid-cols-6 gap-4 py-4 items-center border-b border-gray-200/80">
                <div className="font-semibold text-gray-700">
                  {data.jadwal.date}
                </div>
                {renderPrayerTimes(data.jadwal, true)}
              </div>

              {/* Tomorrow's Times */}
              {data.jadwal_tomorrow && (
                <div className="grid grid-cols-6 gap-4 py-4 items-center">
                  <div className="font-semibold text-gray-700">
                    {data.jadwal_tomorrow.date}
                  </div>
                  {renderPrayerTimes(data.jadwal_tomorrow, false)}
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <section id="jadwal-sholat" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <Card className="w-full max-w-4xl mx-auto shadow-xl overflow-hidden bg-white/80 backdrop-blur-sm border-gray-200/50">
          <CardContent className="p-6 md:p-8">{renderContent()}</CardContent>
        </Card>
      </div>
    </section>
  );
};

export default JadwalSholatSection;
