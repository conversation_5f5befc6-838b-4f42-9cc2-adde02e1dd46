const Event = require("../models/Event");
const Tag = require("../models/Tag");

// @desc    Create an event
exports.createEvent = async (req, res) => {
  const {
    title,
    slug,
    description,
    speaker,
    start_time,
    end_time,
    location,
    image_url,
    tags,
  } = req.body;
  try {
    const existingSlug = await Event.findBySlug(slug);
    if (existingSlug) {
      return res.status(400).json({ message: "Slug already exists" });
    }

    const eventId = await Event.create(
      title,
      slug,
      description,
      speaker,
      start_time,
      end_time,
      location,
      image_url,
      req.user.id
    );

    if (tags && tags.length > 0) {
      const tagIds = await Promise.all(
        tags.map((tagName) => Tag.findOrCreate(tagName))
      );
      // Note: We will need to create linkToEvent in Tag model
      await Tag.linkToEvent(eventId, tagIds);
    }

    res.status(201).json({ message: "Event created successfully", eventId });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Check if a slug exists
exports.checkSlug = async (req, res) => {
  const { slug, currentId } = req.body;
  try {
    const event = await Event.findBySlug(slug);
    if (event && event.id.toString() !== currentId) {
      return res.json({ exists: true });
    }
    res.json({ exists: false });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get single event by ID
exports.getEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }
    // Ambil juga tags
    const tags = await Tag.getTagsByEventId(req.params.id);
    event.tags = tags;
    res.json(event);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get all events (optional range filter: ?start=YYYY-MM-DD&end=YYYY-MM-DD)
exports.getAllEvents = async (req, res) => {
  try {
    const { start, end } = req.query;
    if (start && end) {
      const events = await Event.findInRange(
        `${start} 00:00:00`,
        `${end} 23:59:59`
      );
      return res.json(events);
    }
    const events = await Event.findAll();
    res.json(events);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Get single event by slug
exports.getEventBySlug = async (req, res) => {
  try {
    const event = await Event.findBySlug(req.params.slug);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }
    res.json(event);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Update an event
exports.updateEvent = async (req, res) => {
  const {
    title,
    slug,
    description,
    speaker,
    start_time,
    end_time,
    location,
    image_url,
    tags,
  } = req.body;
  try {
    let event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    if (
      event.author_id.toString() !== req.user.id.toString() &&
      !req.user.roles.includes("admin")
    ) {
      return res.status(401).json({ message: "User not authorized" });
    }

    const existingSlug = await Event.findBySlug(slug);
    if (existingSlug && existingSlug.id.toString() !== req.params.id) {
      return res.status(400).json({ message: "Slug already exists" });
    }

    const affectedRows = await Event.update(
      req.params.id,
      title,
      slug,
      description,
      speaker,
      start_time,
      end_time,
      location,
      image_url
    );

    if (tags) {
      const tagIds = await Promise.all(
        tags.map((tagName) => Tag.findOrCreate(tagName))
      );
      // Note: We will need to create unlinkFromEvent in Tag model
      await Tag.unlinkFromEvent(req.params.id);
      await Tag.linkToEvent(req.params.id, tagIds);
    }

    if (affectedRows > 0) {
      res.json({ message: "Event updated" });
    } else {
      res.status(400).json({ message: "Event could not be updated" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// @desc    Delete an event
exports.deleteEvent = async (req, res) => {
  try {
    let event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    if (
      event.author_id.toString() !== req.user.id.toString() &&
      !req.user.roles.includes("admin")
    ) {
      return res.status(401).json({ message: "User not authorized" });
    }

    const affectedRows = await Event.delete(req.params.id);
    if (affectedRows > 0) {
      res.json({ message: "Event removed" });
    } else {
      res.status(404).json({ message: "Event not found" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};
