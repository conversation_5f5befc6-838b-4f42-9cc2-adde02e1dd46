-- ===== BASE SETUP =====
CREATE DATABASE IF NOT EXISTS nabawi
  DEFAULT CHARACTER SET utf8mb4
  DEFAULT COLLATE utf8mb4_unicode_ci;
USE nabawi;

-- Tips: ka<PERSON> ma<PERSON>h <PERSON> lawas, set innodb_file_per_table=ON untuk efisiensi storage.

-- ===== ROLES =====
CREATE TABLE IF NOT EXISTS roles (
  id SMALLINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,        -- k<PERSON>il saja, role gak banyak
  name VARCHAR(50) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== USERS =====
CREATE TABLE IF NOT EXISTS users (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(100) NOT NULL,
  password CHAR(60) NOT NULL,                             -- bcrypt 60 chars
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uq_users_username (username),
  KEY idx_users_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== USER_ROLES (junction) =====
CREATE TABLE IF NOT EXISTS user_roles (
  user_id INT UNSIGNED NOT NULL,
  role_id SMALLINT UNSIGNED NOT NULL,
  PRIMARY KEY (user_id, role_id),
  CONSTRAINT fk_user_roles_user
    FOREIGN KEY (user_id) REFERENCES users(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT fk_user_roles_role
    FOREIGN KEY (role_id) REFERENCES roles(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  KEY idx_user_roles_role (role_id)                       -- bantu query by role
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== ARTICLES =====
CREATE TABLE IF NOT EXISTS articles (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  slug VARCHAR(191) NOT NULL,                             -- 191 aman untuk unique index di utf8mb4
  content MEDIUMTEXT NOT NULL,                            -- artikel bisa panjang
  featured_image_url VARCHAR(512) NULL,                   -- URL bisa panjang
  author_id INT UNSIGNED NOT NULL,
  status ENUM('draft','published','archived') NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uq_articles_slug (slug),
  KEY idx_articles_author (author_id),
  KEY idx_articles_status_created (status, created_at),   -- listing by status & newest
  FULLTEXT KEY ftx_articles_title_content (title, content), -- search (MySQL 5.6+ InnoDB ok)
  CONSTRAINT fk_articles_author
    FOREIGN KEY (author_id) REFERENCES users(id)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== EVENTS =====
CREATE TABLE IF NOT EXISTS events (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  slug VARCHAR(191) NOT NULL,
  description MEDIUMTEXT NOT NULL,
  speaker VARCHAR(150) NULL,
  start_time DATETIME NOT NULL,
  end_time   DATETIME NOT NULL,
  location VARCHAR(150) NULL,
  image_url VARCHAR(512) NULL,
  author_id INT UNSIGNED NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uq_events_slug (slug),
  KEY idx_events_time (start_time, end_time),
  KEY idx_events_author (author_id),
  FULLTEXT KEY ftx_events_title_desc (title, description),   -- search event
  CONSTRAINT fk_events_author
    FOREIGN KEY (author_id) REFERENCES users(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT chk_events_time CHECK (end_time >= start_time)  -- MySQL 8+ enforce
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== TAGS =====
CREATE TABLE IF NOT EXISTS tags (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,             -- longgar, tag bisa banyak
  name VARCHAR(100) NOT NULL,
  UNIQUE KEY uq_tags_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== ARTICLE_TAGS (junction) =====
CREATE TABLE IF NOT EXISTS article_tags (
  article_id INT UNSIGNED NOT NULL,
  tag_id INT UNSIGNED NOT NULL,
  PRIMARY KEY (article_id, tag_id),
  CONSTRAINT fk_article_tags_article
    FOREIGN KEY (article_id) REFERENCES articles(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT fk_article_tags_tag
    FOREIGN KEY (tag_id) REFERENCES tags(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  KEY idx_article_tags_tag (tag_id)                       -- bantu filter by tag
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== EVENT_TAGS (junction) =====
CREATE TABLE IF NOT EXISTS event_tags (
  event_id INT UNSIGNED NOT NULL,
  tag_id INT UNSIGNED NOT NULL,
  PRIMARY KEY (event_id, tag_id),
  CONSTRAINT fk_event_tags_event
    FOREIGN KEY (event_id) REFERENCES events(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT fk_event_tags_tag
    FOREIGN KEY (tag_id) REFERENCES tags(id)
    ON DELETE CASCADE ON UPDATE CASCADE,
  KEY idx_event_tags_tag (tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ===== SEED (idempotent-ish) =====
INSERT IGNORE INTO roles (name) VALUES ('admin'), ('article_creator'), ('event_creator');

-- password: password123 (sudah bcrypt 60 char)
INSERT IGNORE INTO users (username, password)
VALUES ('naufal48', '$2b$10$VXChYsvbybkWLJaGrLRDmOXRN8Wl3syg8mbOSd0HLoMd.pTeHstS.');

INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u JOIN roles r ON r.name='admin' WHERE u.username='naufal48';

-- tags dulu (tanpa hardcode id)
INSERT IGNORE INTO tags (name) VALUES
('Artikel Islam'), ('Kajian'), ('Pengumuman'),
('Fiqh Kontemporer'), ('Ramadhan'), ('Ilmu Islam'),
('Hadits'), ('Tahsin');

-- articles
INSERT IGNORE INTO articles (title, slug, content, featured_image_url, author_id, status, created_at, updated_at) VALUES
('Adab Menuntut Ilmu dalam Islam: Panduan Lengkap untuk Pencari Ilmu',
 'adab-menuntut-ilmu-dalam-islam-panduan-lengkap-untuk-pencari-ilmu',
 '<figure class="image"><img style="aspect-ratio:1411/1411;" src="http://localhost:3001/uploads/1754497326452.jpg" width="1411" height="1411"></figure><p>Menuntut ilmu adalah kewajiban setiap Muslim. Artikel ini membahas adab-adab yang harus dijaga dalam proses menuntut ilmu menurut ajaran Islam.</p>',
 NULL, (SELECT id FROM users WHERE username='naufal48'),
 'published', '2025-08-06 23:08:19', '2025-08-06 23:22:08'
),
('Hikmah Dibalik Sholat Berjamaah: Manfaat Spiritual dan Sosial',
 'hikmah-dibalik-sholat-berjamaah-manfaat-spiritual-dan-sosial',
 '<figure class="image"><img style="aspect-ratio:500/300;" src="https://images.unsplash.com/photo-1542816417-0983c9c9ad53?w=500&h=300&fit=crop" width="500" height="300"></figure><p>Sholat berjamaah bukan hanya ibadah ritual, tetapi memiliki hikmah mendalam untuk kehidupan spiritual dan sosial umat Muslim.</p>',
 NULL, (SELECT id FROM users WHERE username='naufal48'),
 'published', '2025-08-06 23:10:08', '2025-08-06 23:10:08'
),
('Pengumuman: Jadwal Kajian Bulan Ramadhan 1446 H',
 'pengumuman-jadwal-kajian-bulan-ramadhan-1446-h',
 '<figure class="image"><img style="aspect-ratio:500/300;" src="https://images.unsplash.com/photo-1591604466107-ec97de577aff?w=500&h=300&fit=crop" width="500" height="300"></figure><p>Informasi lengkap tentang jadwal kajian spesial dan program-program spiritual selama bulan suci Ramadhan mendatang.</p>',
 NULL, (SELECT id FROM users WHERE username='naufal48'),
 'published', '2025-08-06 23:10:43', '2025-08-06 23:10:43'
),
('Memahami Konsep Tazkiyah dalam Perspektif Al-Quran dan Hadits',
 'memahami-konsep-tazkiyah-dalam-perspektif-al-quran-dan-hadits',
 '<figure class="image"><img style="aspect-ratio:500/300;" src="http://localhost:3001/uploads/1754497112691.jpg" width="500" height="300"></figure><p>Tazkiyah atau penyucian jiwa adalah konsep fundamental dalam Islam. Mari kita pahami lebih dalam berdasarkan Al-Quran dan Hadits.</p>',
 NULL, (SELECT id FROM users WHERE username='naufal48'),
 'published', '2025-08-06 23:11:12', '2025-08-06 23:18:34'
);

-- map article->tags by slug & tag name
INSERT IGNORE INTO article_tags (article_id, tag_id)
SELECT a.id, t.id FROM articles a JOIN tags t
WHERE a.slug='adab-menuntut-ilmu-dalam-islam-panduan-lengkap-untuk-pencari-ilmu' AND t.name='Artikel Islam';
INSERT IGNORE INTO article_tags (article_id, tag_id)
SELECT a.id, t.id FROM articles a JOIN tags t
WHERE a.slug='memahami-konsep-tazkiyah-dalam-perspektif-al-quran-dan-hadits' AND t.name='Artikel Islam';
INSERT IGNORE INTO article_tags (article_id, tag_id)
SELECT a.id, t.id FROM articles a JOIN tags t
WHERE a.slug='hikmah-dibalik-sholat-berjamaah-manfaat-spiritual-dan-sosial' AND t.name='Kajian';
INSERT IGNORE INTO article_tags (article_id, tag_id)
SELECT a.id, t.id FROM articles a JOIN tags t
WHERE a.slug='pengumuman-jadwal-kajian-bulan-ramadhan-1446-h' AND t.name='Pengumuman';

-- events
INSERT IGNORE INTO events (title, slug, description, speaker, start_time, end_time, location, image_url, author_id, created_at, updated_at) VALUES
('Kajian Kitab Riyadhus Shalihin', 'kajian-kitab-riyadhus-shalihin',
'Kajian spesial membahas Kitab Riyadhus Shalihin karya Imam Nawawi bersama Ustadz Dr. Syafiq Riza Basalamah, MA.
Kitab ini berisi kumpulan hadits pilihan seputar keutamaan amal, akhlak, dan tarbiyah ruhani.
Kajian ini terbuka untuk umum, baik ikhwan maupun akhwat.
Tersedia tempat duduk terpisah & konsumsi ringan untuk peserta.',
'Ustadz Dr. Syafiq Riza Basalamah, MA',
'2025-08-23 18:00:00','2025-08-23 20:00:00','Masjid An Nabawi Hall','/uploads/1754537993632.jpg',
(SELECT id FROM users WHERE username='naufal48'), '2025-08-07 10:39:53','2025-08-07 10:42:32'
),
('Siroh Nabawiyah: Periode Madinah', 'siroh-nabawiyah-periode-madinah',
'Kajian sejarah Islam bersama Ustadz Muhammad Farid, M.A. membahas fase penting dalam kehidupan Rasulullah ﷺ, yaitu Periode Madinah.
Pembahasan meliputi peristiwa hijrah, pembentukan masyarakat Islam, peperangan besar seperti Badar & Uhud, hingga perkembangan dakwah secara global.
Kajian terbuka untuk umum dan sangat cocok bagi siapa pun yang ingin memahami sejarah Islam secara menyeluruh dan mendalam.',
'Ustadz Muhammad Farid, M.A.',
'2025-08-11 13:00:00','2025-08-11 15:00:00','Masjid An Nabawi - Aula','/uploads/1754539339423.png',
(SELECT id FROM users WHERE username='naufal48'), '2025-08-07 11:02:19','2025-08-07 11:02:19'
),
('Fiqh Muamalah dalam Era Digital', 'fiqh-muamalah-dalam-era-digital',
'Memahami hukum-hukum Islam dalam transaksi dan bisnis modern',
'Ustadz Dr. Abdullah Hakim',
'2025-08-14 19:00:00','2025-08-14 20:00:00','Online via Zoom','/uploads/1754539664892.png',
(SELECT id FROM users WHERE username='naufal48'), '2025-08-07 11:07:44','2025-08-07 11:07:44'
),
('Tahsin Al-Qur''an untuk Pemula', 'tahsin-al-quran-untuk-pemula',
'Belajar membaca Al-Qur''an dengan tajwid yang benar untuk tingkat pemula',
'Ustadzah Siti Aminah, Lc.',
'2025-08-15 08:00:00','2025-08-15 11:00:00','Masjid An Nabawi - Ruang Tahsin','/uploads/1754539764927.jpg',
(SELECT id FROM users WHERE username='naufal48'), '2025-08-07 11:09:24','2025-08-07 11:09:24'
);

-- map event->tags (pakai nama tag)
INSERT IGNORE INTO event_tags (event_id, tag_id)
SELECT e.id, t.id FROM events e JOIN tags t
WHERE e.slug='kajian-kitab-riyadhus-shalihin' AND t.name='Artikel Islam';
INSERT IGNORE INTO event_tags (event_id, tag_id)
SELECT e.id, t.id FROM events e JOIN tags t
WHERE e.slug='siroh-nabawiyah-periode-madinah' AND t.name='Kajian';
INSERT IGNORE INTO event_tags (event_id, tag_id)
SELECT e.id, t.id FROM events e JOIN tags t
WHERE e.slug='fiqh-muamalah-dalam-era-digital' AND t.name='Fiqh Kontemporer';
INSERT IGNORE INTO event_tags (event_id, tag_id)
SELECT e.id, t.id FROM events e JOIN tags t
WHERE e.slug='tahsin-al-quran-untuk-pemula' AND t.name='Tahsin';
