require("dotenv").config();
const express = require("express");
const cors = require("cors");

const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Serve static files from the 'uploads' directory
app.use("/uploads", express.static("uploads"));

app.get("/", (req, res) => {
  res.send("Backend server is running!");
});

// Define Routes
app.use("/api/auth", require("./routes/auth"));
app.use("/api/users", require("./routes/users"));
app.use("/api/roles", require("./routes/roles"));
app.use("/api/tags", require("./routes/tags"));
app.use("/api/upload", require("./routes/upload"));
app.use("/api/articles", require("./routes/articles"));
app.use("/api/events", require("./routes/events"));
app.use("/api/dashboard", require("./routes/dashboard"));
app.use("/api/ziswaf", require("./routes/ziswaf"));

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
