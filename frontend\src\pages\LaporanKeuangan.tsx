import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface ByType { type: string; total: number; }
interface ByMonth { month: string; total: number; }

const LaporanKeuangan = () => {
  const [byType, setByType] = useState<ByType[]>([]);
  const [byMonth, setByMonth] = useState<ByMonth[]>([]);

  useEffect(() => {
    const load = async () => {
      const res = await fetch("http://localhost:3001/api/ziswaf/reports/finance/summary");
      const data = await res.json();
      setByType(data.byType || []);
      setByMonth(data.byMonth || []);
    };
    load();
  }, []);

  return (
    <div className="min-h-screen font-inter">
      <div className="container mx-auto px-4 py-12 max-w-5xl space-y-8">
        <div>
          <h1 className="font-poppins text-3xl text-islamic-green font-bold"><PERSON><PERSON><PERSON></h1>
          <p className="text-muted-foreground mt-2">Ringkasan donasi per jenis dan 12 bulan terakhir (dummy).</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Ringkasan per Jenis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              {byType.map((t) => (
                <div key={t.type} className="border rounded-md p-4">
                  <div className="text-sm text-muted-foreground">{t.type}</div>
                  <div className="text-2xl font-semibold">Rp{Number(t.total).toLocaleString()}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>12 Bulan Terakhir</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {byMonth.map((m) => (
                <div key={m.month} className="flex justify-between border-b py-1 text-sm">
                  <span>{m.month}</span>
                  <span>Rp{Number(m.total).toLocaleString()}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LaporanKeuangan;

