import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link, useNavigate } from "react-router-dom";
import Logo from "@/assets/logo.png";

const Register = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("http://localhost:3001/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        // Mendaftarkan pengguna dengan role 'article_creator' (ID: 2) dan 'event_creator' (ID: 3)
        body: JSON.stringify({ username, password, roleIds: [2, 3] }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Registrasi gagal");
      }

      setSuccess("Registrasi berhasil! Anda akan diarahkan ke halaman login.");
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Terjadi kesalahan saat registrasi.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="flex min-h-screen w-full items-center justify-center bg-cover bg-center p-4"
      style={{ backgroundImage: `url('/src/assets/hero-bg.jpg')` }}
    >
      <div className="absolute inset-0 bg-islamic-green/80 backdrop-blur-sm"></div>
      <Card className="relative z-10 w-full max-w-md border-islamic-gold/50 bg-white/90 shadow-2xl">
        <CardHeader className="text-center">
          <img src={Logo} alt="An-Nabawi Logo" className="mx-auto mb-4 h-20" />
          <CardTitle className="font-amiri text-3xl text-islamic-green">
            Buat Akun Baru
          </CardTitle>
          <CardDescription className="text-gray-600">
            Daftar untuk menjadi kontributor
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRegister}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="username" className="text-islamic-green">
                  Username
                </Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Pilih username unik"
                  required
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password" className="text-islamic-green">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  required
                  placeholder="Buat password yang kuat"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-islamic-green/50 focus:border-islamic-green focus:ring-islamic-gold"
                />
              </div>
              {error && (
                <p className="text-red-500 text-sm text-center">{error}</p>
              )}
              {success && (
                <p className="text-green-600 text-sm text-center">{success}</p>
              )}
              <Button
                type="submit"
                variant="islamic"
                className="w-full"
                disabled={loading}
              >
                {loading ? "Mendaftar..." : "Register"}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="mt-4 text-center text-sm text-gray-600">
            Sudah punya akun?{" "}
            <Link
              to="/login"
              className="font-semibold text-islamic-green hover:text-islamic-gold hover:underline"
            >
              Login
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Register;
