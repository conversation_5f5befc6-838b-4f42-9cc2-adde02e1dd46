import { useState, useEffect } from "react";

interface HijriDateState {
  date: string | null;
  loading: boolean;
  error: string | null;
}

const useHijriDate = () => {
  const [hijriDate, setHijriDate] = useState<HijriDateState>({
    date: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    const fetchHijriDate = async () => {
      setHijriDate({ date: null, loading: true, error: null });
      try {
        const response = await fetch("https://api.myquran.com/v2/cal/hijr");
        if (!response.ok) {
          throw new Error(`Failed to fetch Hijri date: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.status || !data.data.date || !data.data.date[1]) {
          throw new Error("Invalid API response for Hijri date.");
        }
        setHijriDate({ date: data.data.date[1], loading: false, error: null });
      } catch (err) {
        if (err instanceof Error) {
          setHijriDate({ date: null, loading: false, error: err.message });
        } else {
          setHijriDate({
            date: null,
            loading: false,
            error: "An unknown error occurred.",
          });
        }
      }
    };

    fetchHijriDate();
  }, []);

  return hijriDate;
};

export default useHijriDate;
