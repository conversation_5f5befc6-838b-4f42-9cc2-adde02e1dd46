import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import Logo from "@/assets/logo.png";

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const isHomePage = location.pathname === "/";

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    if (isHomePage) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    } else {
      navigate(`/#${sectionId}`);
    }
    setIsOpen(false);
  };

  const navLinks = [
    { label: "Home", sectionId: "hero" },
    { label: "Jadwal Sholat", sectionId: "jadwal-sholat" },
    { label: "Kajian", sectionId: "kajian" },
    { label: "Kalender Kajian", path: "/kajian-kalender" },
    { label: "ZISWAF", path: "/ziswaf" },
    { label: "Laporan Keuangan", path: "/laporan-keuangan" },
    { label: "Artikel", sectionId: "artikel" },
    { label: "Kontak", sectionId: "kontak" },
  ];

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled || !isHomePage
          ? "bg-background/95 backdrop-blur-md border-b border-border shadow-sm"
          : "bg-transparent border-b border-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center space-x-3">
            <img src={Logo} alt="An Nabawi Logo" className="w-10 h-10" />
            <div>
              <h1
                className={`font-poppins font-bold leading-tight transition-colors ${
                  isScrolled || !isHomePage
                    ? "text-islamic-green"
                    : "text-white"
                }`}
              >
                An Nabawi
              </h1>
              <p
                className={`text-xs leading-tight transition-colors ${
                  isScrolled || !isHomePage
                    ? "text-muted-foreground"
                    : "text-gray-200"
                }`}
              >
                Islamic Center
              </p>
            </div>
          </Link>

          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) =>
              link.path ? (
                <Link
                  key={link.label}
                  to={link.path}
                  className={`font-medium transition-colors ${
                    isScrolled || !isHomePage
                      ? "text-foreground hover:text-islamic-green"
                      : "text-white hover:text-gray-300"
                  }`}
                >
                  {link.label}
                </Link>
              ) : (
                <button
                  key={link.sectionId}
                  onClick={() => scrollToSection(link.sectionId!)}
                  className={`font-medium transition-colors ${
                    isScrolled || !isHomePage
                      ? "text-foreground hover:text-islamic-green"
                      : "text-white hover:text-gray-300"
                  }`}
                >
                  {link.label}
                </button>
              )
            )}
            <Button
              variant="islamic"
              size="sm"
              onClick={() => scrollToSection("download-app")}
              className="ml-4"
            >
              Download App
            </Button>
          </div>

          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(!isOpen)}
              className={`${
                isScrolled || !isHomePage ? "text-foreground" : "text-white"
              }`}
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {isOpen && (
          <div className="md:hidden py-4 border-t border-border bg-background">
            <div className="flex flex-col space-y-4">
              {navLinks.map((link) =>
                link.path ? (
                  <Link
                    key={link.label}
                    to={link.path}
                    onClick={() => setIsOpen(false)}
                    className="text-left text-foreground hover:text-islamic-green transition-colors font-medium px-4 py-2"
                  >
                    {link.label}
                  </Link>
                ) : (
                  <button
                    key={link.sectionId}
                    onClick={() => scrollToSection(link.sectionId!)}
                    className="text-left text-foreground hover:text-islamic-green transition-colors font-medium px-4 py-2"
                  >
                    {link.label}
                  </button>
                )
              )}
              <div className="px-4 pt-2">
                <Button
                  variant="islamic"
                  size="sm"
                  onClick={() => scrollToSection("download-app")}
                  className="w-full"
                >
                  Download App
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
