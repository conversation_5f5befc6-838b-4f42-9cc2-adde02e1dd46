import { Smartphone, Download, Star, Users, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/assets/logo.png";

const DownloadAppSection = () => {
  return (
    <section id="download-app" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-islamic-green rounded-full flex items-center justify-center">
              <Smartphone className="h-6 w-6 text-white" />
            </div>
            <h2 className="font-poppins font-bold text-3xl md:text-4xl text-islamic-green">
              Aplikasi Mobile An Nabawi
            </h2>
          </div>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Dapatkan pengalaman lebih lengkap dengan aplikasi mobile kami
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="order-2 lg:order-1">
              <h3 className="font-poppins font-bold text-2xl md:text-3xl text-islamic-green mb-6">
                Semua yang Anda Butuhkan dalam Satu Aplikasi
              </h3>

              <div className="space-y-6 mb-8">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-islamic-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Clock className="h-5 w-5 text-islamic-green" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg text-islamic-green mb-2">
                      Jadwal Sholat Akurat
                    </h4>
                    <p className="text-muted-foreground">
                      Waktu sholat yang selalu update berdasarkan lokasi Anda,
                      lengkap dengan adzan dan pengingat
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-islamic-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Users className="h-5 w-5 text-islamic-green" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg text-islamic-green mb-2">
                      Kajian & Event
                    </h4>
                    <p className="text-muted-foreground">
                      Akses mudah ke semua jadwal kajian, pendaftaran acara, dan
                      live streaming kajian
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-islamic-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Star className="h-5 w-5 text-islamic-green" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg text-islamic-green mb-2">
                      Konten Islami
                    </h4>
                    <p className="text-muted-foreground">
                      Ribuan artikel, video kajian, dan audio murotal untuk
                      memperkaya wawasan Islam Anda
                    </p>
                  </div>
                </div>
              </div>

              {/* Download Buttons */}
              <div className="space-y-4">
                <h4 className="font-semibold text-lg text-islamic-green">
                  Download Sekarang:
                </h4>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="islamic"
                    size="lg"
                    className="flex items-center gap-3 px-6 py-4"
                  >
                    <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                      <svg
                        viewBox="0 0 24 24"
                        className="w-5 h-5 text-islamic-green"
                      >
                        <path
                          fill="currentColor"
                          d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <div className="text-xs">Download di</div>
                      <div className="font-bold">Google Play</div>
                    </div>
                  </Button>

                  <Button
                    variant="islamic-outline"
                    size="lg"
                    className="flex items-center gap-3 px-6 py-4"
                  >
                    <div className="w-8 h-8 bg-islamic-green rounded-lg flex items-center justify-center">
                      <svg viewBox="0 0 24 24" className="w-5 h-5 text-white">
                        <path
                          fill="currentColor"
                          d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.1 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <div className="text-xs">Download di</div>
                      <div className="font-bold">App Store</div>
                    </div>
                  </Button>
                </div>
              </div>

              {/* App Stats */}
              <div className="mt-8 grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="font-bold text-2xl text-islamic-green">
                    10K+
                  </div>
                  <div className="text-sm text-muted-foreground">Downloads</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-2xl text-islamic-green">
                    4.8
                  </div>
                  <div className="text-sm text-muted-foreground">Rating</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-2xl text-islamic-green">
                    500+
                  </div>
                  <div className="text-sm text-muted-foreground">Reviews</div>
                </div>
              </div>
            </div>

            {/* Phone Mockup */}
            <div className="order-1 lg:order-2 flex justify-center">
              <div className="relative">
                {/* Phone Frame */}
                <div className="bg-gray-800 p-2 rounded-3xl shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-300">
                  <div className="bg-white rounded-2xl p-6 w-64 h-96 flex flex-col">
                    {/* Status Bar */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-xs font-medium">9:41</div>
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                        <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                        <div className="w-1 h-1 bg-islamic-green rounded-full"></div>
                      </div>
                    </div>

                    {/* App Header */}
                    <div className="text-center mb-6">
                      <img
                        src={Logo}
                        alt="An Nabawi App"
                        className="w-12 h-12 mx-auto mb-2"
                      />
                      <h3 className="font-bold text-islamic-green">
                        An Nabawi
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        Islamic Center
                      </p>
                    </div>

                    {/* App Content Preview */}
                    <div className="flex-1 space-y-3">
                      <div className="bg-islamic-green/10 rounded-lg p-3">
                        <div className="text-xs font-medium text-islamic-green mb-1">
                          Sholat Selanjutnya
                        </div>
                        <div className="text-lg font-bold">Dzuhur - 12:05</div>
                      </div>

                      <div className="space-y-2">
                        <div className="h-3 bg-gray-100 rounded"></div>
                        <div className="h-3 bg-gray-100 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-islamic-gold/10 rounded p-2 text-center">
                          <div className="w-6 h-6 bg-islamic-gold/20 rounded mx-auto mb-1"></div>
                          <div className="text-xs">Kajian</div>
                        </div>
                        <div className="bg-islamic-green/10 rounded p-2 text-center">
                          <div className="w-6 h-6 bg-islamic-green/20 rounded mx-auto mb-1"></div>
                          <div className="text-xs">Qibla</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DownloadAppSection;
