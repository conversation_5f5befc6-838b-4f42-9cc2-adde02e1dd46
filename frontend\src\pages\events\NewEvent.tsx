import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/components/ui/sonner";
import { useNavigate } from "react-router-dom";
import CreatableSelect from "react-select/creatable";
import { debounce } from "lodash";
import { ArrowLeft } from "lucide-react";

interface TagOption {
  readonly label: string;
  readonly value: string;
}

const NewEvent = () => {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [description, setDescription] = useState("");
  const [speaker, setSpeaker] = useState("");
  const [startTime, setStartTime] = useState<Date | undefined>(undefined);
  const [endTime, setEndTime] = useState<Date | undefined>(undefined);
  const [location, setLocation] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [tags, setTags] = useState<readonly TagOption[]>([]);
  const [tagOptions, setTagOptions] = useState<readonly TagOption[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slugError, setSlugError] = useState<string | null>(null);
  const { token } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchTags = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/tags", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        const data = await response.json();
        const options = data.map((tag: string) => ({
          value: tag,
          label: tag,
        }));
        setTagOptions(options);
      } catch (error) {
        // Silently ignore
      }
    };
    if (token) {
      fetchTags();
    }
  }, [token]);

  const generateSlug = (str: string) => {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-");
  };

  const checkSlugUniqueness = useCallback(
    debounce(async (currentSlug: string) => {
      if (!currentSlug) {
        setSlugError(null);
        return;
      }
      // Note: We need to create a check-slug endpoint for events
      // For now, we assume it exists and works like the article one.
      try {
        const response = await fetch(
          "http://localhost:3001/api/events/check-slug",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ slug: currentSlug }),
          }
        );
        const data = await response.json();
        if (data.exists) {
          setSlugError("Slug already exists. Please choose another one.");
        } else {
          setSlugError(null);
        }
      } catch (error) {
        // Silently ignore
      }
    }, 500),
    [token]
  );

  useEffect(() => {
    const newSlug = generateSlug(title);
    setSlug(newSlug);
    checkSlugUniqueness(newSlug);
  }, [title, checkSlugUniqueness]);

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value;
    setSlug(newSlug);
    checkSlugUniqueness(newSlug);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImage(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (slugError) {
      toast.error("Please fix the errors before submitting.");
      return;
    }
    setIsSubmitting(true);

    let imageUrl = "";
    if (image) {
      const formData = new FormData();
      formData.append("image", image);
      try {
        const uploadResponse = await fetch("http://localhost:3001/api/upload", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
          body: formData,
        });
        const uploadData = await uploadResponse.json();
        if (!uploadResponse.ok) {
          throw new Error(uploadData.message || "Image upload failed");
        }
        imageUrl = uploadData.filePath;
      } catch (error) {
        toast.error(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const response = await fetch("http://localhost:3001/api/events", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          slug,
          description,
          speaker,
          start_time: startTime?.toISOString(),
          end_time: endTime?.toISOString(),
          location,
          image_url: imageUrl,
          tags: tags.map((t) => t.value),
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create event");
      }
      toast.success("Event created successfully!");
      navigate("/dashboard/events");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="grid gap-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={() => navigate("/dashboard/events")}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="flex-1 shrink-0 whitespace-nowrap text-xl font-semibold tracking-tight sm:grow-0">
          Add New Event
        </h1>
      </div>
      <Card>
        <CardContent className="pt-6">
          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter event title"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={slug}
                onChange={handleSlugChange}
                placeholder="event-slug-will-be-here"
                required
              />
              {slugError && (
                <p className="text-sm text-red-500 mt-1">{slugError}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="speaker">Speaker</Label>
              <Input
                id="speaker"
                value={speaker}
                onChange={(e) => setSpeaker(e.target.value)}
                placeholder="Enter speaker's name"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-time">Start Time</Label>
                <DateTimePicker date={startTime} setDate={setStartTime} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time">End Time</Label>
                <DateTimePicker date={endTime} setDate={setEndTime} />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder="e.g., Main Hall, Online"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the event"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image">Featured Image</Label>
              <Input id="image" type="file" onChange={handleImageChange} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <CreatableSelect
                isMulti
                options={tagOptions}
                value={tags}
                onChange={(newValue) => setTags(newValue)}
                onCreateOption={(inputValue) => {
                  const newTag = { value: inputValue, label: inputValue };
                  setTagOptions((prev) => [...prev, newTag]);
                  setTags((prev) => [...prev, newTag]);
                }}
                placeholder="Select or create tags"
                styles={{
                  menu: (provided) => ({ ...provided, zIndex: 9999 }),
                }}
              />
            </div>
            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting || !!slugError}>
                {isSubmitting ? "Creating..." : "Create Event"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewEvent;
