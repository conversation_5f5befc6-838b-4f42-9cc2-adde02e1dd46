import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Badge } from "@/components/ui/badge";
import { Calendar, User } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface Article {
  title: string;
  content: string;
  author_name: string;
  created_at: string;
  featured_image_url: string | null;
  tags: string[];
}

const ArticleDetail = () => {
  const [article, setArticle] = useState<Article | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { slug } = useParams<{ slug: string }>();

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        const response = await fetch(
          `http://localhost:3001/api/articles/slug/${slug}`
        );
        if (!response.ok) throw new Error("Article not found");
        const data = await response.json();
        setArticle(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      } finally {
        setIsLoading(false);
      }
    };
    fetchArticle();
  }, [slug]);

  const formatDate = (dateString: string) => {
    if (!dateString) return "Invalid Date";
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const extractFirstImage = (html: string | null | undefined) => {
    if (!html) return null;
    const imgTag = html.match(/<img[^>]+src="([^">]+)"/);
    return imgTag ? imgTag[1] : null;
  };

  const removeFirstImage = (html: string | null | undefined) => {
    if (!html) return "";
    return html.replace(/<figure class="image"><img[^>]+><\/figure>/, "");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen font-inter">
        <Navigation />
        <div className="container mx-auto px-4 py-12 max-w-4xl">
          <Skeleton className="h-12 w-3/4 mb-4" />
          <Skeleton className="h-6 w-1/2 mb-8" />
          <Skeleton className="w-full h-96 mb-8" />
          <Skeleton className="h-6 w-full mb-4" />
          <Skeleton className="h-6 w-full mb-4" />
          <Skeleton className="h-6 w-5/6 mb-4" />
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen font-inter flex flex-col">
        <Navigation />
        <div className="flex-grow flex items-center justify-center text-center">
          <div>
            <h1 className="text-4xl font-bold text-islamic-green mb-4">
              404 - Article Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              The article you are looking for does not exist.
            </p>
            <Link to="/" className="text-islamic-gold hover:underline">
              Return to Home
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const featuredImage =
    article.featured_image_url || extractFirstImage(article.content);

  return (
    <div className="min-h-screen font-inter">
      <Navigation />
      <main className="container mx-auto px-4 pt-28 pb-12 max-w-4xl">
        <article>
          <header className="mb-8">
            <h1 className="font-amiri text-4xl md:text-5xl font-bold text-islamic-green mb-4 leading-tight">
              {article.title}
            </h1>
            <div className="flex items-center gap-6 text-sm text-muted-foreground mb-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{article.author_name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(article.created_at)}</span>
              </div>
            </div>
            {article.tags && article.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {article.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </header>

          {featuredImage && (
            <img
              src={featuredImage}
              alt={article.title}
              className="w-full h-auto max-h-[500px] object-cover rounded-lg mb-8"
            />
          )}

          <div
            className="prose lg:prose-xl max-w-none"
            dangerouslySetInnerHTML={{
              __html: removeFirstImage(article.content),
            }}
          />
        </article>
      </main>
      <Footer />
    </div>
  );
};

export default ArticleDetail;
