const db = require("../config/db");

const Tag = {
  findOrCreate: async (tagName) => {
    // Cari tag
    let [rows] = await db.query("SELECT id FROM tags WHERE name = ?", [
      tagName,
    ]);
    if (rows.length > 0) {
      return rows[0].id;
    }
    // Jika tidak ada, buat baru
    const [result] = await db.query("INSERT INTO tags (name) VALUES (?)", [
      tagName,
    ]);
    return result.insertId;
  },

  linkToArticle: async (articleId, tagIds) => {
    if (tagIds && tagIds.length > 0) {
      const values = tagIds.map((tagId) => [articleId, tagId]);
      await db.query("INSERT INTO article_tags (article_id, tag_id) VALUES ?", [
        values,
      ]);
    }
  },

  unlinkFromArticle: async (articleId) => {
    await db.query("DELETE FROM article_tags WHERE article_id = ?", [
      articleId,
    ]);
  },

  getTagsByArticleId: async (articleId) => {
    const [rows] = await db.query(
      `
      SELECT t.name 
      FROM tags t
      JOIN article_tags at ON t.id = at.tag_id
      WHERE at.article_id = ?
    `,
      [articleId]
    );
    return rows.map((row) => row.name);
  },

  linkToEvent: async (eventId, tagIds) => {
    if (tagIds && tagIds.length > 0) {
      const values = tagIds.map((tagId) => [eventId, tagId]);
      await db.query("INSERT INTO event_tags (event_id, tag_id) VALUES ?", [
        values,
      ]);
    }
  },

  unlinkFromEvent: async (eventId) => {
    await db.query("DELETE FROM event_tags WHERE event_id = ?", [eventId]);
  },

  getTagsByEventId: async (eventId) => {
    const [rows] = await db.query(
      `
      SELECT t.name 
      FROM tags t
      JOIN event_tags et ON t.id = et.tag_id
      WHERE et.event_id = ?
    `,
      [eventId]
    );
    return rows.map((row) => row.name);
  },
};

module.exports = Tag;
