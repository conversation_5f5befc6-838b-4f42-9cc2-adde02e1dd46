import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/components/ui/sonner";
import { useNavigate, useParams } from "react-router-dom";
import CreatableSelect from "react-select/creatable";
import { debounce } from "lodash";
import { ArrowLeft } from "lucide-react";

interface TagOption {
  readonly label: string;
  readonly value: string;
}

const EditEvent = () => {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [description, setDescription] = useState("");
  const [speaker, setSpeaker] = useState("");
  const [startTime, setStartTime] = useState<Date | undefined>(undefined);
  const [endTime, setEndTime] = useState<Date | undefined>(undefined);
  const [location, setLocation] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [existingImageUrl, setExistingImageUrl] = useState<string | undefined>(
    undefined
  );
  const [tags, setTags] = useState<readonly TagOption[]>([]);
  const [tagOptions, setTagOptions] = useState<readonly TagOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slugError, setSlugError] = useState<string | null>(null);
  const { token } = useAuth();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const checkSlugUniqueness = useCallback(
    debounce(async (currentSlug: string) => {
      if (!currentSlug) {
        setSlugError(null);
        return;
      }
      try {
        const response = await fetch(
          "http://localhost:3001/api/events/check-slug",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ slug: currentSlug, currentId: id }),
          }
        );
        const data = await response.json();
        if (data.exists) {
          setSlugError("Slug already exists. Please choose another one.");
        } else {
          setSlugError(null);
        }
      } catch (error) {
        // Silently ignore
      }
    }, 500),
    [token, id]
  );

  useEffect(() => {
    const fetchAllTags = async () => {
      try {
        const response = await fetch("http://localhost:3001/api/tags", {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!response.ok) throw new Error("Failed to fetch tags");
        const data = await response.json();
        setTagOptions(data.map((tag: string) => ({ value: tag, label: tag })));
      } catch (error) {
        // silent fail
      }
    };

    const fetchEvent = async () => {
      try {
        const response = await fetch(
          `http://localhost:3001/api/events/id/${id}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!response.ok) throw new Error("Failed to fetch event");
        const data = await response.json();
        setTitle(data.title);
        setSlug(data.slug);
        setDescription(data.description);
        setSpeaker(data.speaker);
        setStartTime(data.start_time ? new Date(data.start_time) : undefined);
        setEndTime(data.end_time ? new Date(data.end_time) : undefined);
        setLocation(data.location);
        setExistingImageUrl(data.image_url);
        setTags(
          (data.tags || []).map((tag: string) => ({ value: tag, label: tag }))
        );
      } catch (error) {
        toast.error(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
        navigate("/dashboard/events");
      } finally {
        setIsLoading(false);
      }
    };
    if (token && id) {
      fetchAllTags();
      fetchEvent();
    }
  }, [id, token, navigate]);

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value;
    setSlug(newSlug);
    checkSlugUniqueness(newSlug);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImage(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (slugError) {
      toast.error("Please fix the errors before submitting.");
      return;
    }
    setIsSubmitting(true);

    let imageUrl = existingImageUrl;
    if (image) {
      const formData = new FormData();
      formData.append("image", image);
      try {
        const uploadResponse = await fetch("http://localhost:3001/api/upload", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
          body: formData,
        });
        const uploadData = await uploadResponse.json();
        if (!uploadResponse.ok) {
          throw new Error(uploadData.message || "Image upload failed");
        }
        imageUrl = uploadData.filePath;
      } catch (error) {
        toast.error(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const response = await fetch(`http://localhost:3001/api/events/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          slug,
          description,
          speaker,
          start_time: startTime?.toISOString(),
          end_time: endTime?.toISOString(),
          location,
          image_url: imageUrl,
          tags: tags.map((t) => t.value),
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to update event");
      }
      toast.success("Event updated successfully!");
      navigate("/dashboard/events");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <p>Loading event...</p>;
  }

  return (
    <div className="grid gap-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={() => navigate("/dashboard/events")}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="flex-1 shrink-0 whitespace-nowrap text-xl font-semibold tracking-tight sm:grow-0">
          Edit Event
        </h1>
      </div>
      <Card>
        <CardContent className="pt-6">
          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={slug}
                onChange={handleSlugChange}
                required
              />
              {slugError && (
                <p className="text-sm text-red-500 mt-1">{slugError}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="speaker">Speaker</Label>
              <Input
                id="speaker"
                value={speaker}
                onChange={(e) => setSpeaker(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-time">Start Time</Label>
                <DateTimePicker date={startTime} setDate={setStartTime} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time">End Time</Label>
                <DateTimePicker date={endTime} setDate={setEndTime} />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image">Featured Image</Label>
              <Input id="image" type="file" onChange={handleImageChange} />
              {existingImageUrl && !image && (
                <div className="mt-2">
                  <p className="text-sm text-gray-500">Current image:</p>
                  <img
                    src={`http://localhost:3001${existingImageUrl}`}
                    alt="Current event"
                    className="w-32 h-32 object-cover rounded-md mt-1"
                  />
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <CreatableSelect
                isMulti
                options={tagOptions}
                value={tags}
                onChange={(newValue) => setTags(newValue)}
                onCreateOption={(inputValue) => {
                  const newTag = { value: inputValue, label: inputValue };
                  setTagOptions((prev) => [...prev, newTag]);
                  setTags((prev) => [...prev, newTag]);
                }}
                placeholder="Select or create tags"
                styles={{
                  menu: (provided) => ({ ...provided, zIndex: 9999 }),
                }}
              />
            </div>
            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting || !!slugError}>
                {isSubmitting ? "Updating..." : "Update Event"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditEvent;
