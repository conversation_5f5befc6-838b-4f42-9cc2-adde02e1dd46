const db = require("../config/db");

const Ziswaf = {
  createDonation: async ({ user_id = null, guest_name, guest_email, guest_phone, type, amount, message }) => {
    const [result] = await db.query(
      `INSERT INTO donations (user_id, guest_name, guest_email, guest_phone, type, amount, message)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [user_id, guest_name, guest_email, guest_phone, type, amount, message || null]
    );
    return result.insertId;
  },

  getUserDonations: async (userId) => {
    const [rows] = await db.query(
      `SELECT * FROM donations WHERE user_id = ? ORDER BY created_at DESC`,
      [userId]
    );
    return rows;
  },

  getGuestDonations: async ({ email, phone }) => {
    const [rows] = await db.query(
      `SELECT * FROM donations WHERE guest_email = ? AND guest_phone = ? ORDER BY created_at DESC`,
      [email, phone]
    );
    return rows;
  },

  getFinanceSummary: async () => {
    const [byType] = await db.query(
      `SELECT type, SUM(amount) as total FROM donations GROUP BY type`
    );
    const [byMonth] = await db.query(
      `SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(amount) as total
       FROM donations
       GROUP BY month
       ORDER BY month DESC
       LIMIT 12`
    );
    return { byType, byMonth };
  },
};

module.exports = Ziswaf;

