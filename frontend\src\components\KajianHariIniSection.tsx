import { Calendar, Clock, MapPin, User } from "lucide-react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

interface EventItem {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
}

interface Props {
  events: EventItem[];
  isLoading: boolean;
}

const isSameDay = (a: Date, b: Date) =>
  a.getFullYear() === b.getFullYear() &&
  a.getMonth() === b.getMonth() &&
  a.getDate() === b.getDate();

const KajianHariIniSection = ({ events, isLoading }: Props) => {
  const today = new Date();
  const todayEvents = (events || []).filter((e) =>
    isSameDay(new Date(e.start_time), today)
  );

  return (
    <section id="kajian-hari-ini" className="py-16 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="font-poppins font-bold text-2xl text-islamic-green">
            Jadwal Kajian Hari Ini
          </h2>
          <p className="text-muted-foreground">
            Sorotan kajian yang berlangsung hari ini
          </p>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-1/2 mb-2" />
                  <Skeleton className="h-4 w-1/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : todayEvents.length === 0 ? (
          <div className="text-center text-muted-foreground">
            Belum ada kajian hari ini.
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {todayEvents.slice(0, 4).map((event) => (
              <Card key={event.id} className="hover:shadow-md">
                <CardHeader>
                  <CardTitle className="font-poppins text-lg text-islamic-green">
                    {event.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{event.speaker}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(event.start_time), "EEEE, d MMM yyyy")}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>
                      {format(new Date(event.start_time), "HH:mm")} - {format(new Date(event.end_time), "HH:mm")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{event.location}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default KajianHariIniSection;

