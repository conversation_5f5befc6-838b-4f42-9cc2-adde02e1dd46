const Article = require("../models/Article");
const Event = require("../models/Event");

exports.getStats = async (req, res) => {
  try {
    const totalArticles = await Article.countPublished();
    const upcomingEvents = await Event.countUpcoming();

    res.json({
      totalArticles,
      upcomingEvents,
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};
