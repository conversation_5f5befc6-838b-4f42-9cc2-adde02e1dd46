const path = require("path");

exports.uploadImage = (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: "No file uploaded." });
  }

  // Mengembalikan path file yang dapat diakses
  const filePath = `/uploads/${req.file.filename}`;

  // Respon disesuaikan untuk bisa dipakai oleh form event dan CKEditor
  res.json({
    uploaded: true, // Untuk kompatibilitas CKEditor
    url: `${req.protocol}://${req.get("host")}${filePath}`, // Untuk CKEditor
    filePath: filePath, // Untuk form event
  });
};
